[profile.default]
auto_detect_solc = false
bytecode_hash = "none"
evm_version = "shanghai"
fuzz = { runs = 250 } # default
gas_reports = ["*"]
optimizer = true
optimizer_runs = 200
out = "out"
script = "script"
solc = "0.8.26"
src = "src"
test = "test"
libs = ["node_modules"]
fs_permissions = [
    { access = "read-write", path = "./deployments/genesis/consensus-registry-config.yaml" },
    { access = "read-write", path = "./deployments/genesis/its-config.yaml" },
    { access = "read-write", path = "./deployments/deployments.json" },
    { access = "read", path = "./external/uniswap/precompiles" },
]

[profile.ci]
fuzz = { runs = 10_000 }
verbosity = 4

[fmt]
bracket_spacing = true
int_types = "long"
line_length = 120
multiline_func_header = "all"
number_underscore = "thousands"
quote_style = "double"
tab_width = 4
wrap_comments = true

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
