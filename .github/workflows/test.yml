name: CI

on:
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened, review_requested, ready_for_review]

env:
  FOUNDRY_PROFILE: ci

jobs:
  check:
    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest]

    name: Foundry project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Set Up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Install Node.js Dependencis
        run: npm install

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Show Forge version
        run: |
          forge --version

      - name: Run Forge fmt
        run: |
          forge fmt --check
        id: fmt

      - name: Run Forge build
        run: |
          forge build --sizes
        id: build

      - name: Run Forge tests
        env:
          POLYGON_RPC_URL: ${{ secrets.POLYGON_RPC_URL }}
        run: |
          forge test -vvv
        id: test
