{"abi": [{"type": "constructor", "inputs": [{"name": "genesisConfig_", "type": "tuple", "internalType": "struct IStakeManager.StakeConfig", "components": [{"name": "stakeAmount", "type": "uint256", "internalType": "uint256"}, {"name": "minWithdrawAmount", "type": "uint256", "internalType": "uint256"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}]}, {"name": "initialValidators_", "type": "tuple[]", "internalType": "struct IConsensusRegistry.ValidatorInfo[]", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}, {"name": "owner_", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "SYSTEM_ADDRESS", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "activate", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allocateIssuance", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "applyIncentives", "inputs": [{"name": "rewardInfos", "type": "tuple[]", "internalType": "struct RewardInfo[]", "components": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "consensusHeaderCount", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "applySlashes", "inputs": [{"name": "slashes", "type": "tuple[]", "internalType": "struct Slash[]", "components": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "approve", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "beginExit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burn", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimStakeRewards", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "concludeEpoch", "inputs": [{"name": "futureCommittee", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegateStake", "inputs": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "validatorSig", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "delegationDigest", "inputs": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "delegator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "epochInfo", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "blockHeight", "type": "uint64", "internalType": "uint64"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "futureEpochInfo", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "blockHeight", "type": "uint64", "internalType": "uint64"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "getApproved", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getBalance", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCommitteeValidators", "inputs": [{"name": "epoch", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IConsensusRegistry.ValidatorInfo[]", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentEpoch", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentEpochInfo", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IConsensusRegistry.EpochInfo", "components": [{"name": "committee", "type": "address[]", "internalType": "address[]"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "blockHeight", "type": "uint64", "internalType": "uint64"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentStakeConfig", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IStakeManager.StakeConfig", "components": [{"name": "stakeAmount", "type": "uint256", "internalType": "uint256"}, {"name": "minWithdrawAmount", "type": "uint256", "internalType": "uint256"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentStakeVersion", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "getEpochInfo", "inputs": [{"name": "epoch", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IConsensusRegistry.EpochInfo", "components": [{"name": "committee", "type": "address[]", "internalType": "address[]"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "blockHeight", "type": "uint64", "internalType": "uint64"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRewards", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getValidator", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "stateMutability": "view"}, {"type": "function", "name": "getValidators", "inputs": [{"name": "status", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IConsensusRegistry.ValidatorInfo[]", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "stateMutability": "view"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isRetired", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "issuance", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address payable"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownerOf", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stake", "inputs": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "stakeConfig", "inputs": [{"name": "version", "type": "uint8", "internalType": "uint8"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IStakeManager.StakeConfig", "components": [{"name": "stakeAmount", "type": "uint256", "internalType": "uint256"}, {"name": "minWithdrawAmount", "type": "uint256", "internalType": "uint256"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tokenByIndex", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "tokenOfOwnerByIndex", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "tokenURI", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "unstake", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeStakeVersion", "inputs": [{"name": "newConfig", "type": "tuple", "internalType": "struct IStakeManager.StakeConfig", "components": [{"name": "stakeAmount", "type": "uint256", "internalType": "uint256"}, {"name": "minWithdrawAmount", "type": "uint256", "internalType": "uint256"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "validators", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "NewEpoch", "inputs": [{"name": "epoch", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.EpochInfo", "components": [{"name": "committee", "type": "address[]", "internalType": "address[]"}, {"name": "epochIssuance", "type": "uint256", "internalType": "uint256"}, {"name": "blockHeight", "type": "uint64", "internalType": "uint64"}, {"name": "epochDuration", "type": "uint32", "internalType": "uint32"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RewardsClaimed", "inputs": [{"name": "claimant", "type": "address", "indexed": false, "internalType": "address"}, {"name": "rewards", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ValidatorActivated", "inputs": [{"name": "validator", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "event", "name": "ValidatorExited", "inputs": [{"name": "validator", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "event", "name": "ValidatorPendingActivation", "inputs": [{"name": "validator", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "event", "name": "ValidatorPendingExit", "inputs": [{"name": "validator", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "event", "name": "ValidatorRetired", "inputs": [{"name": "validator", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "event", "name": "ValidatorSlashed", "inputs": [{"name": "slash", "type": "tuple", "indexed": false, "internalType": "struct Slash", "components": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}], "anonymous": false}, {"type": "event", "name": "ValidatorStaked", "inputs": [{"name": "validator", "type": "tuple", "indexed": false, "internalType": "struct IConsensusRegistry.ValidatorInfo", "components": [{"name": "bls<PERSON><PERSON><PERSON>", "type": "bytes", "internalType": "bytes"}, {"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}, {"name": "activationEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "exitEpoch", "type": "uint32", "internalType": "uint32"}, {"name": "currentStatus", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}, {"name": "isRetired", "type": "bool", "internalType": "bool"}, {"name": "isDelegated", "type": "bool", "internalType": "bool"}, {"name": "stakeVersion", "type": "uint8", "internalType": "uint8"}]}], "anonymous": false}, {"type": "error", "name": "AlreadyDefined", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "CommitteeRequirement", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "DuplicateBLSPubkey", "inputs": []}, {"type": "error", "name": "ERC721EnumerableForbiddenBatchMint", "inputs": []}, {"type": "error", "name": "ERC721IncorrectOwner", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InsufficientApproval", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC721InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721NonexistentToken", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC721OutOfBoundsIndex", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "index", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "GenesisArityMismatch", "inputs": []}, {"type": "error", "name": "InsufficientRewards", "inputs": [{"name": "withdrawAmount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidBLSPubkey", "inputs": []}, {"type": "error", "name": "InvalidCommitteeSize", "inputs": [{"name": "minCommitteeSize", "type": "uint256", "internalType": "uint256"}, {"name": "providedCommitteeSize", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidDuration", "inputs": [{"name": "duration", "type": "uint32", "internalType": "uint32"}]}, {"type": "error", "name": "InvalidEpoch", "inputs": [{"name": "epoch", "type": "uint32", "internalType": "uint32"}]}, {"type": "error", "name": "InvalidProof", "inputs": []}, {"type": "error", "name": "InvalidStakeAmount", "inputs": [{"name": "stakeAmount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidStatus", "inputs": [{"name": "status", "type": "uint8", "internalType": "enum IConsensusRegistry.ValidatorStatus"}]}, {"type": "error", "name": "InvalidSupply", "inputs": []}, {"type": "error", "name": "InvalidTokenId", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidValidatorAddress", "inputs": []}, {"type": "error", "name": "LowLevelCallFailure", "inputs": []}, {"type": "error", "name": "NotRecipient", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NotTransferable", "inputs": []}, {"type": "error", "name": "NotValidator", "inputs": [{"name": "validator<PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OnlySystemCall", "inputs": [{"name": "invalidCaller", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Reentrancy", "inputs": []}, {"type": "error", "name": "RequiresConsensusNFT", "inputs": []}, {"type": "error", "name": "StringsInsufficientHexLength", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "length", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "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", "sourceMap": "955:32436:204:-:0;;;29969:3005;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;30121:6;2829:78:206;;;;;;;;;;;;;-1:-1:-1;;;2829:78:206;;;;;;;;;;;;;;;;-1:-1:-1;;;2829:78:206;;;2890:4;2896:6;1454:5:140;1446;:13;;;;;;:::i;:::-;-1:-1:-1;1469:7:140;:17;1479:7;1469;:17;:::i;:::-;-1:-1:-1;;2157:4:185;2119:45;;-1:-1:-1;2191:13:185;2174:30;;2215:18;;2331:23;11450:36:206;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;11450:36:206;;;;;11337:156;2331:23:185;2431:22;;;;;;;2545:25;;;;;;;;;2580:26;;;;2616:32;;;;2828:4;2822:11;;2893:16;2883:27;;2934:12;;;2927:30;;;;2981:12;;;2974:33;3045:9;3038:4;3031:12;;3024:31;3093:9;3086:4;3079:12;;3072:31;3146:4;3133:18;;3184:34;;-1:-1:-1;;;;;;;;1273:26:121;;;-1:-1:-1;1269:95:121;;1322:31;;-1:-1:-1;;;1322:31:121;;1350:1;1322:31;;;8052:51:226;8025:18;;1322:31:121;;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;30192:25:204;;:30;;:78:::2;;-1:-1:-1::0;30226:25:204;;30254:16:::2;-1:-1:-1::0;30192:78:204::2;30188:138;;;30293:22;;-1:-1:-1::0;;;30293:22:204::2;;;;;;;;;;;30188:138;30442:4;30421:27;;;;;:::i;:::-;-1:-1:-1::0;;;;;8070:32:226;;;8052:51;;8040:2;8025:18;30421:27:204::2;;;;;;;;;;;;;;;;::::0;::::2;;;;;-1:-1:-1::0;30402:8:204::2;:47:::0;;-1:-1:-1;;;;;;30402:47:204::2;-1:-1:-1::0;;;;;30402:47:204;;;::::2;::::0;;;::::2;::::0;;-1:-1:-1;30459:11:204;;;:8:::2;:11;::::0;;;:28;;:11;:28;;::::2;::::0;;;:11;:28;::::2;::::0;;;::::2;::::0;::::2;::::0;;;;-1:-1:-1;;30459:28:204::2;;::::0;;::::2;::::0;;;::::2;::::0;;30498:220:::2;30519:1;30514;:6;30498:220;;30541:23;30567:9;30577:1;30567:12;;;;;;;:::i;:::-;;;;30541:38;;30615:14;:28;;;30593:5;:19;;;:50;;;;;;;;;;;;;;;;;;30679:14;:28;;;30657:5;:19;;:50;;;;30527:191;30522:3;;;;:::i;:::-;;;30498:220;;;;30733:9;30728:2240;30748:18;:25;30744:1;:29;30728:2240;;;30794:37;30834:18;30853:1;30834:21;;;;;;;;:::i;:::-;;;;;;;30794:61;;30949:16;:26;;;:33;30986:2;30949:39;30945:103;;31015:18;;-1:-1:-1::0;;;31015:18:204::2;;;;;;;;;;;30945:103;31065:33;::::0;::::2;::::0;-1:-1:-1;;;;;31065:49:204::2;31061:120;;31141:25;;-1:-1:-1::0;;;31141:25:204::2;;;;;;;;;;;31061:120;31198:32;::::0;::::2;::::0;:45:::2;;::::0;31194:137:::2;;31283:32;::::0;;::::2;::::0;31270:46;;-1:-1:-1;;;31270:46:204;;8791:10:226;8779:23;;;31270:46:204::2;::::0;::::2;8761:42:226::0;8734:18;;31270:46:204::2;8617:192:226::0;31194:137:204::2;31348:26;::::0;::::2;::::0;:39:::2;;::::0;31344:125:::2;;31427:26;::::0;::::2;::::0;31414:40:::2;::::0;-1:-1:-1;;;31414:40:204;;8791:10:226;8779:23;;;31414:40:204::2;::::0;::::2;8761:42:226::0;8734:18;;31414:40:204::2;8617:192:226::0;31344:125:204::2;31520:22;31486:16;:30;;;:56;;;;;;;;:::i;:::-;;31482:147;;31583:16;:30;;;31569:45;;-1:-1:-1::0;;;31569:45:204::2;;;;;;;;:::i;31482:147::-;31646:26;::::0;::::2;::::0;:35:::2;31642:118;;31722:22;31708:37;;-1:-1:-1::0;;;31708:37:204::2;;;;;;;;:::i;31642:118::-;31777:28;::::0;::::2;::::0;:36:::2;;31809:4;31777:36:::0;31773:426:::2;;31953:231;::::0;;::::2;::::0;::::2;::::0;;31995:26;;31985:37;;::::2;::::0;;::::2;::::0;31953:231;;32044:33;;::::2;::::0;;-1:-1:-1;;;;;31953:231:204;;::::2;::::0;;::::2;::::0;;;;;::::2;::::0;;;;;;-1:-1:-1;31953:231:204;;;;;;32164:1:::2;31953:231:::0;;;;;;31916:33;;31904:46;::::2;::::0;;:11:::2;:46:::0;;;;;;:280;;;;;;;;::::2;::::0;;-1:-1:-1;;;;;;31904:280:204::2;::::0;;::::2;::::0;;;::::2;::::0;;;;;::::2;::::0;;::::2;::::0;;;;;;;;;::::2;-1:-1:-1::0;;;;;;31904:280:204;;;;;;;-1:-1:-1;;;31953:231:204::2;31904:280:::0;;::::2;::::0;;;::::2;::::0;;;::::2;-1:-1:-1::0;;;;;;;;31904:280:204::2;-1:-1:-1::0;;;;;;;;31904:280:204;;::::2;::::0;;;::::2;;::::0;;31773:426:::2;32216:29;::::0;::::2;::::0;:34:::2;;::::0;32212:129:::2;;32296:29;::::0;::::2;::::0;32277:49:::2;::::0;-1:-1:-1;;;32277:49:204;;9674:4:226;9662:17;;;32277:49:204::2;::::0;::::2;9644:36:226::0;9617:18;;32277:49:204::2;9500:186:226::0;32212:129:204::2;32430:9;32425:210;32446:1;32441;:6;32425:210;;32472:9;32482:1;32472:12;;;;;;;:::i;:::-;32500:33;::::0;;::::2;::::0;32472:12:::2;::::0;;;::::2;::::0;;;::::2;:62:::0;;::::2;::::0;::::2;::::0;;-1:-1:-1;32472:62:204;;;;;;;;::::2;::::0;;-1:-1:-1;;;;;;32472:62:204::2;-1:-1:-1::0;;;;;32472:62:204;;::::2;::::0;;;::::2;::::0;;32552:15:::2;32568:1:::0;32552:18:::2;::::0;::::2;;;;;:::i;:::-;32586:33;::::0;;::::2;::::0;32552:18:::2;::::0;;;::::2;::::0;;;::::2;:68:::0;;::::2;::::0;::::2;::::0;;-1:-1:-1;32552:68:204;;;;;;;;::::2;::::0;;-1:-1:-1;;;;;;32552:68:204::2;-1:-1:-1::0;;;;;32552:68:204;;::::2;::::0;;;::::2;::::0;;32449:3:::2;::::0;::::2;:::i;:::-;;;32425:210;;;-1:-1:-1::0;32660:33:204::2;::::0;;::::2;::::0;-1:-1:-1;;;;;32649:45:204::2;;::::0;;;:10:::2;:45:::0;;;;;;:64;;32697:16;;32649:45;;;:64:::2;::::0;:45;:64:::2;:::i;:::-;-1:-1:-1::0;32649:64:204::2;::::0;::::2;::::0;::::2;::::0;::::2;::::0;;::::2;::::0;::::2;::::0;::::2;::::0;::::2;::::0;::::2;::::0;;::::2;-1:-1:-1::0;;;32649:64:204::2;-1:-1:-1::0;;;;32649:64:204;;;::::2;-1:-1:-1::0;;;32649:64:204::2;-1:-1:-1::0;;;;;;32649:64:204;;;-1:-1:-1;;;;;32649:64:204;;::::2;::::0;;;;;;;::::2;::::0;;::::2;::::0;::::2;::::0;;::::2;::::0;::::2;::::0;;-1:-1:-1;;;;32649:64:204;;;-1:-1:-1;;;;32649:64:204;;;;-1:-1:-1;;;32649:64:204;::::2;::::0;::::2;;;;;;:::i;:::-;;;::::0;;-1:-1:-1;32649:64:204::2;::::0;::::2;::::0;::::2;::::0;;::::2;::::0;;::::2;::::0;::::2;::::0;::::2;::::0;;::::2;::::0;::::2;;-1:-1:-1::0;;;32649:64:204::2;-1:-1:-1::0;;;;;32649:64:204;::::2;;-1:-1:-1::0;;;32649:64:204::2;-1:-1:-1::0;;;;32649:64:204;::::2;;-1:-1:-1::0;;;32649:64:204::2;::::0;;;;-1:-1:-1;;;;32649:64:204;;;;;;;;;;::::2;::::0;;;::::2;;::::0;;32773:26;;32736:33:::2;::::0;;::::2;::::0;;-1:-1:-1;;;;;32727:43:204::2;-1:-1:-1::0;32727:43:204;;;:8:::2;:43:::0;;;;;;;:72;;;;32819:33;32813:88:::2;::::0;32854:46:::2;32819:33:::0;-1:-1:-1;;;;;10945:32:206;;10856:128;32854:46:204::2;32813:5;:88::i;:::-;32921:36;32940:16;32921:36;;;;;;:::i;:::-;;;;;;;;-1:-1:-1::0;30775:3:204::2;;30728:2240;;;;29969:3005:::0;;;955:32436;;2912:187:121;3004:6;;;-1:-1:-1;;;;;3020:17:121;;;3004:6;3020:17;;;-1:-1:-1;;;;;;3020:17:121;;;;;;3052:40;;3004:6;;;;;;;;3052:40;;2985:16;;3052:40;2975:124;2912:187;:::o;9978:327:140:-;-1:-1:-1;;;;;10045:16:140;;10041:87;;10084:33;;-1:-1:-1;;;10084:33:140;;10114:1;10084:33;;;8052:51:226;8025:18;;10084:33:140;7906:203:226;10041:87:140;10137:21;10161:32;10169:2;10173:7;10137:21;10161:7;:32::i;:::-;10137:56;-1:-1:-1;;;;;;10207:27:140;;;10203:96;;10257:31;;-1:-1:-1;;;10257:31:140;;10285:1;10257:31;;;8052:51:226;8025:18;;10257:31:140;7906:203:226;10203:96:140;10031:274;9978:327;;:::o;2518:625:143:-;2613:7;;2656:32;2670:2;2674:7;2683:4;2656:13;:32::i;:::-;2632:56;-1:-1:-1;;;;;;2703:27:143;;2699:210;;2746:40;2778:7;3949:10;:17;;3922:24;;;;:15;:24;;;;;:44;;;3976:24;;;;;;;;;;;;3846:161;2746:40;2699:210;;;2824:2;-1:-1:-1;;;;;2807:19:143;:13;-1:-1:-1;;;;;2807:19:143;;2803:106;;2842:56;2875:13;2890:7;2842:32;:56::i;:::-;-1:-1:-1;;;;;2922:16:143;;2918:188;;2954:45;2991:7;2954:36;:45::i;:::-;2918:188;;;3037:2;-1:-1:-1;;;;;3020:19:143;:13;-1:-1:-1;;;;;3020:19:143;;3016:90;;3055:40;3083:2;3087:7;3055:27;:40::i;:::-;3123:13;2518:625;-1:-1:-1;;;;2518:625:143:o;8861:795:140:-;8947:7;5824:16;;;:7;:16;;;;;;-1:-1:-1;;;;;5824:16:140;;;;9058:18;;;9054:86;;9092:37;9109:4;9115;9121:7;9092:16;:37::i;:::-;-1:-1:-1;;;;;9184:18:140;;;9180:256;;9300:48;9317:1;9321:7;9317:1;;9300:8;:48::i;:::-;-1:-1:-1;;;;;9391:15:140;;;;;;:9;:15;;;;;:20;;-1:-1:-1;;9391:20:140;;;9180:256;-1:-1:-1;;;;;9450:16:140;;;9446:107;;-1:-1:-1;;;;;9510:13:140;;;;;;:9;:13;;;;;:18;;9527:1;9510:18;;;9446:107;9563:16;;;;:7;:16;;;;;;:21;;-1:-1:-1;;;;;;9563:21:140;-1:-1:-1;;;;;9563:21:140;;;;;;;;;9600:27;;9563:16;;9600:27;;;;;;;9645:4;8861:795;-1:-1:-1;;;;8861:795:140:o;4624:1055:143:-;4886:22;4911:15;4921:4;4911:9;:15::i;:::-;4936:18;4957:26;;;:17;:26;;;;;;;;;-1:-1:-1;;;;;5058:18:143;;;;:12;:18;;;;;;4886:40;;-1:-1:-1;4957:26:143;5180:28;;;5176:325;;5224:19;5246:35;;;;;;;;;;;;5296:31;;;;;;:45;;;5413:30;;:17;:30;;;;;:43;;;5176:325;5594:26;;;;:17;:26;;;;;;;;5587:33;;;5637:35;;;;-1:-1:-1;5637:35:143;;5630:42;-1:-1:-1;4624:1055:143:o;5967:1061::-;6241:10;:17;6216:22;;6241:21;;6261:1;;6241:21;:::i;:::-;6272:18;6293:24;;;:15;:24;;;;;;6661:10;:26;;6216:46;;-1:-1:-1;6293:24:143;;6216:46;;6661:26;;;;;;:::i;:::-;;;;;;;;;6639:48;;6723:11;6698:10;6709;6698:22;;;;;;;;:::i;:::-;;;;;;;;;;;;:36;;;;6802:28;;;:15;:28;;;;;;;:41;;;6971:24;;;;;6964:31;7005:10;:16;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;6038:990;;;5967:1061;:::o;3437:214::-;3521:14;3554:1;3538:13;3548:2;3538:9;:13::i;:::-;:17;;;;:::i;:::-;-1:-1:-1;;;;;3565:16:143;;;;;;;:12;:16;;;;;;;;:24;;;;;;;;:34;;;3609:26;;;:17;:26;;;;;;:35;;;;-1:-1:-1;3437:214:143:o;7105:368:140:-;7217:38;7231:5;7238:7;7247;7217:13;:38::i;:::-;7212:255;;-1:-1:-1;;;;;7275:19:140;;7271:186;;7321:31;;-1:-1:-1;;;7321:31:140;;;;;12872:25:226;;;12845:18;;7321:31:140;12726:177:226;7271:186:140;7398:44;;-1:-1:-1;;;7398:44:140;;-1:-1:-1;;;;;13100:32:226;;7398:44:140;;;13082:51:226;13149:18;;;13142:34;;;13055:18;;7398:44:140;12908:274:226;14794:662:140;14954:9;:31;;;-1:-1:-1;;;;;;14967:18:140;;;;14954:31;14950:460;;;15001:13;15017:22;15031:7;15017:13;:22::i;:::-;15001:38;-1:-1:-1;;;;;;15167:18:140;;;;;;:35;;;15198:4;-1:-1:-1;;;;;15189:13:140;:5;-1:-1:-1;;;;;15189:13:140;;;15167:35;:69;;;;-1:-1:-1;;;;;;4038:25:140;;;4015:4;4038:25;;;:18;:25;;;;;;;;:35;;;;;;;;;;;;15206:30;15167:69;15163:142;;;15263:27;;-1:-1:-1;;;15263:27:140;;-1:-1:-1;;;;;8070:32:226;;15263:27:140;;;8052:51:226;8025:18;;15263:27:140;7906:203:226;15163:142:140;15323:9;15319:81;;;15377:7;15373:2;-1:-1:-1;;;;;15357:28:140;15366:5;-1:-1:-1;;;;;15357:28:140;;;;;;;;;;;15319:81;14987:423;14950:460;-1:-1:-1;;15420:24:140;;;;:15;:24;;;;;:29;;-1:-1:-1;;;;;;15420:29:140;-1:-1:-1;;;;;15420:29:140;;;;;;;;;;14794:662::o;1919:208::-;1982:7;-1:-1:-1;;;;;2005:19:140;;2001:87;;2047:30;;-1:-1:-1;;;2047:30:140;;2074:1;2047:30;;;8052:51:226;8025:18;;2047:30:140;7906:203:226;2001:87:140;-1:-1:-1;;;;;;2104:16:140;;;;;:9;:16;;;;;;;1919:208::o;6401:272::-;6504:4;-1:-1:-1;;;;;6539:21:140;;;;;;:127;;;6586:7;-1:-1:-1;;;;;6577:16:140;:5;-1:-1:-1;;;;;6577:16:140;;:52;;;-1:-1:-1;;;;;;4038:25:140;;;4015:4;4038:25;;;:18;:25;;;;;;;;:35;;;;;;;;;;;;6597:32;6577:88;;;-1:-1:-1;;6033:7:140;6059:24;;;:15;:24;;;;;;-1:-1:-1;;;;;6059:24:140;;;6633:32;;;;6520:146;-1:-1:-1;6401:272:140:o;16212:241::-;16275:7;5824:16;;;:7;:16;;;;;;-1:-1:-1;;;;;5824:16:140;;16337:88;;16383:31;;-1:-1:-1;;;16383:31:140;;;;;12872:25:226;;;12845:18;;16383:31:140;12726:177:226;16337:88:140;16441:5;16212:241;-1:-1:-1;;16212:241:140:o;955:32436:204:-;;;;;;;;:::o;14:127:226:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:253;218:2;212:9;260:4;248:17;;-1:-1:-1;;;;;280:34:226;;316:22;;;277:62;274:88;;;342:18;;:::i;:::-;378:2;371:22;146:253;:::o;404:255::-;476:2;470:9;518:6;506:19;;-1:-1:-1;;;;;540:34:226;;576:22;;;537:62;534:88;;;602:18;;:::i;664:275::-;735:2;729:9;800:2;781:13;;-1:-1:-1;;777:27:226;765:40;;-1:-1:-1;;;;;820:34:226;;856:22;;;817:62;814:88;;;882:18;;:::i;:::-;918:2;911:22;664:275;;-1:-1:-1;664:275:226:o;944:167::-;1022:13;;1075:10;1064:22;;1054:33;;1044:61;;1101:1;1098;1091:12;1044:61;944:167;;;:::o;1116:250::-;1201:1;1211:113;1225:6;1222:1;1219:13;1211:113;;;1301:11;;;1295:18;1282:11;;;1275:39;1247:2;1240:10;1211:113;;;-1:-1:-1;;1358:1:226;1340:16;;1333:27;1116:250::o;1371:533::-;1424:5;1477:3;1470:4;1462:6;1458:17;1454:27;1444:55;;1495:1;1492;1485:12;1444:55;1522:13;;-1:-1:-1;;;;;1547:30:226;;1544:56;;;1580:18;;:::i;:::-;1624:59;1671:2;1648:17;;-1:-1:-1;;1644:31:226;1677:4;1640:42;1624:59;:::i;:::-;1708:6;1699:7;1692:23;1762:3;1755:4;1746:6;1738;1734:19;1730:30;1727:39;1724:59;;;1779:1;1776;1769:12;1724:59;1792:81;1866:6;1859:4;1850:7;1846:18;1839:4;1831:6;1827:17;1792:81;:::i;1909:177::-;1988:13;;-1:-1:-1;;;;;2030:31:226;;2020:42;;2010:70;;2076:1;2073;2066:12;2091:160;2183:13;;2225:1;2215:12;;2205:40;;2241:1;2238;2231:12;2256:164;2332:13;;2381;;2374:21;2364:32;;2354:60;;2410:1;2407;2400:12;2425:160;2502:13;;2555:4;2544:16;;2534:27;;2524:55;;2575:1;2572;2565:12;2590:2693;2765:6;2773;2781;2825:9;2816:7;2812:23;2855:3;2851:2;2847:12;2844:32;;;2872:1;2869;2862:12;2844:32;2896:4;2892:2;2888:13;2885:33;;;2914:1;2911;2904:12;2885:33;;2940:22;;:::i;:::-;3007:16;;3032:22;;3120:2;3105:18;;;3099:25;3140:14;;;3133:31;3230:2;3215:18;;;3209:25;3250:14;;;3243:31;3306:48;3350:2;3335:18;;3306:48;:::i;:::-;3301:2;3290:14;;3283:72;3423:4;3408:20;;3402:27;3294:5;;-1:-1:-1;;;;;;3441:30:226;;3438:50;;;3484:1;3481;3474:12;3438:50;3507:22;;3560:4;3552:13;;3548:27;-1:-1:-1;3538:55:226;;3589:1;3586;3579:12;3538:55;3616:9;;-1:-1:-1;;;;;3637:30:226;;3634:56;;;3670:18;;:::i;:::-;3716:6;3713:1;3709:14;3743:28;3767:2;3763;3759:11;3743:28;:::i;:::-;3805:19;;;3849:2;3879:11;;;3875:20;;;3840:12;;;;3907:19;;;3904:39;;;3939:1;3936;3929:12;3904:39;3971:2;3967;3963:11;3952:22;;3983:1201;3999:6;3994:3;3991:15;3983:1201;;;4072:10;;-1:-1:-1;;;;;4098:35:226;;4095:55;;;4146:1;4143;4136:12;4095:55;4173:20;;4245:6;4217:16;;;-1:-1:-1;;4213:30:226;4209:43;4206:63;;;4265:1;4262;4255:12;4206:63;4297:22;;:::i;:::-;4362:2;4354:11;;4348:18;-1:-1:-1;;;;;4382:32:226;;4379:52;;;4427:1;4424;4417:12;4379:52;4460:64;4516:7;4511:2;4500:8;4496:2;4492:17;4488:26;4460:64;:::i;:::-;4451:7;4444:81;;4563:42;4601:2;4597;4593:11;4563:42;:::i;:::-;4558:2;4549:7;4545:16;4538:68;4644:41;4681:2;4677;4673:11;4644:41;:::i;:::-;4639:2;4630:7;4626:16;4619:67;4724:43;4761:4;4757:2;4753:13;4724:43;:::i;:::-;4719:2;4710:7;4706:16;4699:69;4808:56;4859:3;4855:2;4851:12;4808:56;:::i;:::-;4801:4;4792:7;4788:18;4781:84;4904:40;4939:3;4935:2;4931:12;4904:40;:::i;:::-;4898:3;4889:7;4885:17;4878:67;4984:40;5019:3;5015:2;5011:12;4984:40;:::i;:::-;4978:3;4969:7;4965:17;4958:67;5064:44;5100:6;5096:2;5092:15;5064:44;:::i;:::-;5058:3;5045:17;;5038:71;5122:20;;-1:-1:-1;5171:2:226;4016:12;;;;5162;;;;3983:1201;;;5203:5;5193:15;;;;;;;5227:50;5272:3;5261:9;5257:19;5227:50;:::i;:::-;5217:60;;2590:2693;;;;;:::o;5288:380::-;5367:1;5363:12;;;;5410;;;5431:61;;5485:4;5477:6;5473:17;5463:27;;5431:61;5538:2;5530:6;5527:14;5507:18;5504:38;5501:161;;5584:10;5579:3;5575:20;5572:1;5565:31;5619:4;5616:1;5609:15;5647:4;5644:1;5637:15;5501:161;;5288:380;;;:::o;5799:518::-;5901:2;5896:3;5893:11;5890:421;;;5937:5;5934:1;5927:16;5981:4;5978:1;5968:18;6051:2;6039:10;6035:19;6032:1;6028:27;6022:4;6018:38;6087:4;6075:10;6072:20;6069:47;;;-1:-1:-1;6110:4:226;6069:47;6165:2;6160:3;6156:12;6153:1;6149:20;6143:4;6139:31;6129:41;;6220:81;6238:2;6231:5;6228:13;6220:81;;;6297:1;6283:16;;6264:1;6253:13;6220:81;;;6224:3;;5799:518;;;:::o;6493:1299::-;6613:10;;-1:-1:-1;;;;;6635:30:226;;6632:56;;;6668:18;;:::i;:::-;6697:97;6787:6;6747:38;6779:4;6773:11;6747:38;:::i;:::-;6741:4;6697:97;:::i;:::-;6843:4;6874:2;6863:14;;6891:1;6886:649;;;;7579:1;7596:6;7593:89;;;-1:-1:-1;7648:19:226;;;7642:26;7593:89;-1:-1:-1;;6450:1:226;6446:11;;;6442:24;6438:29;6428:40;6474:1;6470:11;;;6425:57;7695:81;;6856:930;;6886:649;5746:1;5739:14;;;5783:4;5770:18;;-1:-1:-1;;6922:20:226;;;7040:222;7054:7;7051:1;7048:14;7040:222;;;7136:19;;;7130:26;7115:42;;7243:4;7228:20;;;;7196:1;7184:14;;;;7070:12;7040:222;;;7044:3;7290:6;7281:7;7278:19;7275:201;;;7351:19;;;7345:26;-1:-1:-1;;7434:1:226;7430:14;;;7446:3;7426:24;7422:37;7418:42;7403:58;7388:74;;7275:201;-1:-1:-1;;;;7522:1:226;7506:14;;;7502:22;7489:36;;-1:-1:-1;6493:1299:226:o;8114:127::-;8175:10;8170:3;8166:20;8163:1;8156:31;8206:4;8203:1;8196:15;8230:4;8227:1;8220:15;8246:127;8307:10;8302:3;8298:20;8295:1;8288:31;8338:4;8335:1;8328:15;8362:4;8359:1;8352:15;8378:135;8417:3;8438:17;;;8435:43;;8458:18;;:::i;:::-;-1:-1:-1;8505:1:226;8494:13;;8378:135::o;8814:127::-;8875:10;8870:3;8866:20;8863:1;8856:31;8906:4;8903:1;8896:15;8930:4;8927:1;8920:15;8946:243;9033:1;9026:5;9023:12;9013:143;;9078:10;9073:3;9069:20;9066:1;9059:31;9113:4;9110:1;9103:15;9141:4;9138:1;9131:15;9013:143;9165:18;;8946:243::o;9194:221::-;9347:2;9332:18;;9359:50;9336:9;9391:6;9359:50;:::i;11089:1367::-;11282:2;11271:9;11264:21;11245:4;11320:6;11314:13;11363:6;11358:2;11347:9;11343:18;11336:34;11399:12;11393:19;11449:6;11443:3;11432:9;11428:19;11421:35;11465:86;11544:6;11538:3;11527:9;11523:19;11518:2;11504:12;11500:21;11465:86;:::i;:::-;11600:2;11588:15;;11582:22;-1:-1:-1;;;;;7863:31:226;;11663:2;11648:18;;7851:44;11582:22;-1:-1:-1;11716:2:226;11704:15;;11698:22;8594:10;8583:22;;11778:2;11763:18;;8571:35;11698:22;-1:-1:-1;11831:2:226;11819:15;;11813:22;8594:10;8583:22;;11893:3;11878:19;;8571:35;11813:22;-1:-1:-1;11947:3:226;11939:6;11935:16;11929:23;11907:45;;11961:68;12024:3;12013:9;12009:19;11993:14;11961:68;:::i;:::-;12078:3;12066:16;;12060:23;11063:13;11056:21;12139:3;12124:19;;;11044:34;;;;12181:16;;12175:23;11063:13;11056:21;12254:3;12239:19;;;11044:34;;;;12296:16;;;12290:23;9487:4;9476:16;12370:6;12355:22;;9464:29;-1:-1:-1;;12439:2:226;12418:15;;;-1:-1:-1;;12414:29:226;12399:45;12446:3;12395:55;;11089:1367::o;12461:128::-;12528:9;;;12549:11;;;12546:37;;;12563:18;;:::i;12594:127::-;12655:10;12650:3;12646:20;12643:1;12636:31;12686:4;12683:1;12676:15;12710:4;12707:1;12700:15;12908:274;955:32436:204;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "955:32436:204:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1435:222:143;;;;;;;;;;-1:-1:-1;1435:222:143;;;;;:::i;:::-;;:::i;:::-;;;661:14:226;;654:22;636:41;;624:2;609:18;1435:222:143;;;;;;;;4043:755:204;;;;;;;;;;-1:-1:-1;4043:755:204;;;;;:::i;:::-;;:::i;:::-;;2364:89:140;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;3496:154::-;;;;;;;;;;-1:-1:-1;3496:154:140;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;2816:32:226;;;2798:51;;2786:2;2771:18;3496:154:140;2652:203:226;5766:137:206;;;;;;;;;;-1:-1:-1;5766:137:206;;;;;:::i;:::-;;:::i;2415:1583:204:-;;;;;;;;;;-1:-1:-1;2415:1583:204;;;;;:::i;:::-;;:::i;9165:1579::-;;;;;;:::i;:::-;;:::i;10789:500::-;;;;;;;;;;;;;:::i;2062:102:143:-;;;;;;;;;;-1:-1:-1;2140:10:143;:17;2062:102;;;5119:25:226;;;5107:2;5092:18;2062:102:143;4973:177:226;6630:195:204;;;;;;;;;;-1:-1:-1;6630:195:204;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;12073:899::-;;;;;;;;;;;;;:::i;5422:239:206:-;;;;;;;;;;-1:-1:-1;5422:239:206;;;;;:::i;11328:700:204:-;;;;;;;;;;-1:-1:-1;11328:700:204;;;;;:::i;:::-;;:::i;8441:685::-;;;;;;:::i;:::-;;:::i;1736:255:143:-;;;;;;;;;;-1:-1:-1;1736:255:143;;;;;:::i;:::-;;:::i;453:92:207:-;;;;;;;;;;;;-1:-1:-1;;;;;453:92:207;;15061:174:204;;;:::i;5349:495::-;;;;;;;;;;-1:-1:-1;5349:495:204;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;1143:29::-;;;;;;;;;;-1:-1:-1;1143:29:204;;;;;:::i;:::-;;:::i;:::-;;;;9798:25:226;;;-1:-1:-1;;;;;9859:31:226;;;9854:2;9839:18;;9832:59;9939:10;9927:23;;;9907:18;;;9900:51;;;;9999:4;9987:17;9982:2;9967:18;;9960:45;9785:3;9770:19;1143:29:204;9575:436:226;29402:65:204;;;;;;;;;;;;;:::i;4786:132:140:-;;;;;;;;;;-1:-1:-1;4786:132:140;;;;;:::i;:::-;;:::i;2236:226:143:-;;;;;;;;;;-1:-1:-1;2236:226:143;;;;;:::i;:::-;;:::i;1178:35:204:-;;;;;;;;;;-1:-1:-1;1178:35:204;;;;;:::i;:::-;;:::i;1726:84:152:-;;;;;;;;;;-1:-1:-1;1796:7:152;;;;1726:84;;2184:118:140;;;;;;;;;;-1:-1:-1;2184:118:140;;;;;:::i;:::-;;:::i;4838:129:204:-;;;;;;;;;;;;;:::i;:::-;;;10188:4:226;10176:17;;;10158:36;;10146:2;10131:18;4838:129:204;10016:184:226;14225:405:204;;;;;;;;;;-1:-1:-1;14225:405:204;;;;;:::i;:::-;;:::i;6870:477::-;;;;;;;;;;-1:-1:-1;6870:477:204;;;;;:::i;:::-;;:::i;1919:208:140:-;;;;;;;;;;-1:-1:-1;1919:208:140;;;;;:::i;:::-;;:::i;2293:101:121:-;;;;;;;;;;;;;:::i;7386:292:204:-;;;;;;;;;;-1:-1:-1;7386:292:204;;;;;:::i;:::-;;:::i;1585:785::-;;;;;;;;;;-1:-1:-1;1585:785:204;;;;;:::i;:::-;;:::i;3945:120:206:-;;;;;;;;;;;;;:::i;:::-;;;;;;11045:13:226;;11027:32;;11115:4;11103:17;;;11097:24;11075:20;;;11068:54;11178:4;11166:17;;;11160:24;11138:20;;;11131:54;11245:4;11233:17;;;11227:24;11253:10;11223:41;11201:20;;;11194:71;;;;11014:3;10999:19;;10820:451;29165:61:204;;;;;;;;;;;;;:::i;6989:596:185:-;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;945:31:206:-;;;;;;;;;;-1:-1:-1;945:31:206;;;;-1:-1:-1;;;;;945:31:206;;;14669:353:204;;;;;;;;;;-1:-1:-1;14669:353:204;;;;;:::i;:::-;;:::i;5889:218::-;;;;;;;;;;-1:-1:-1;5889:218:204;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;1638:85:121:-;;;;;;;;;;-1:-1:-1;1710:6:121;;;;;-1:-1:-1;;;;;1710:6:121;1638:85;;2517:93:140;;;;;;;;;;;;;:::i;6152:433:204:-;;;;;;;;;;-1:-1:-1;6152:433:204;;;;;:::i;:::-;;:::i;6008:151:206:-;;;;;;;;;;-1:-1:-1;6008:151:206;;;;;:::i;3779:126::-;;;;;;;;;;-1:-1:-1;3779:126:206;;;;;:::i;:::-;;:::i;4984:233:140:-;;;;;;;;;;-1:-1:-1;4984:233:140;;;;;:::i;:::-;;:::i;5012:92:204:-;;;;;;;;;;-1:-1:-1;5085:12:204;;-1:-1:-1;;;5085:12:204;;;;5012:92;;16120:10:226;16108:23;;;16090:42;;16078:2;16063:18;5012:92:204;15946:192:226;5149:155:204;;;;;;;;;;;;;:::i;33014:375::-;;;;;;;;;;-1:-1:-1;33014:375:204;;;;;:::i;:::-;;:::i;6215:672:206:-;;;;;;;;;;-1:-1:-1;6215:672:206;;;;;:::i;:::-;;:::i;7718:633:204:-;;;;;;;;;;-1:-1:-1;7718:633:204;;;;;:::i;:::-;;:::i;3927:153:140:-;;;;;;;;;;-1:-1:-1;3927:153:140;;;;;:::i;:::-;;:::i;13011:1128:204:-;;;;;;;;;;-1:-1:-1;13011:1128:204;;;;;:::i;:::-;;:::i;2543:215:121:-;;;;;;;;;;-1:-1:-1;2543:215:121;;;;;:::i;:::-;;:::i;3605:134:206:-;;;;;;;;;;-1:-1:-1;3605:134:206;;;;;:::i;:::-;-1:-1:-1;;;;;3706:26:206;3680:7;3706:26;;;:8;:26;;;;;;;3605:134;1219:51:204;;;;;;;;;;-1:-1:-1;1219:51:204;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;;;:::i;1435:222:143:-;1537:4;-1:-1:-1;;;;;;1560:50:143;;-1:-1:-1;;;1560:50:143;;:90;;;1614:36;1638:11;1614:23;:36::i;:::-;1553:97;1435:222;-1:-1:-1;;1435:222:143:o;4043:755:204:-;592:10:207;-1:-1:-1;;;;;592:28:207;588:67;;629:26;;-1:-1:-1;;;629:26:207;;644:10;629:26;;;2798:51:226;2771:18;;629:26:207;;;;;;;;588:67;4139:9:204::1;4134:658;4150:18:::0;;::::1;4134:658;;;4189:20;4212:7;;4220:1;4212:10;;;;;;;:::i;:::-;;;::::0;;;::::1;::::0;-1:-1:-1;4406:33:204::1;::::0;-1:-1:-1;4416:22:204::1;;::::0;::::1;4212:10:::0;4416:22:::1;:::i;4406:33::-;4402:47;;;4441:8;;;4402:47;4503:12;::::0;::::1;::::0;::::1;::::0;4468:8:::1;::::0;:32:::1;::::0;4477:22:::1;::::0;4503:5;4477:22:::1;:::i;:::-;-1:-1:-1::0;;;;;4468:32:204::1;-1:-1:-1::0;;;;;4468:32:204::1;;;;;;;;;;;;;:47;4464:275;;;4571:12;::::0;::::1;::::0;::::1;::::0;4535:8:::1;::::0;:32:::1;::::0;4544:22:::1;::::0;4571:5;4544:22:::1;:::i;:::-;-1:-1:-1::0;;;;;4535:32:204::1;-1:-1:-1::0;;;;;4535:32:204::1;;;;;;;;;;;;;:48;;;;;;;:::i;:::-;::::0;;;-1:-1:-1;4464:275:204::1;::::0;-1:-1:-1;4464:275:204::1;;4686:38;4701:22;;::::0;::::1;:5:::0;:22:::1;:::i;:::-;4686:14;:38::i;:::-;4758:23;4775:5;4758:23;;;;;;:::i;:::-;;;;;;;;4175:617;4134:658;4170:3;;4134:658;;;;4043:755:::0;;:::o;2364:89:140:-;2409:13;2441:5;2434:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2364:89;:::o;3496:154::-;3563:7;3582:22;3596:7;3582:13;:22::i;:::-;-1:-1:-1;6033:7:140;6059:24;;;:15;:24;;;;;;-1:-1:-1;;;;;6059:24:140;3622:21;5963:127;5766:137:206;5879:17;;-1:-1:-1;;;5879:17:206;;;;;;;;;;;2415:1583:204;592:10:207;-1:-1:-1;;;;;592:28:207;588:67;;629:26;;-1:-1:-1;;;629:26:207;;644:10;629:26;;;2798:51:226;2771:18;;629:26:207;2652:203:226;588:67:207;2601:19:204::1;::::0;2671:11;-1:-1:-1;;;;;2657:33:204;::::1;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;-1:-1:-1;2657:33:204::1;;2630:60;;2705:9;2700:778;2716:22:::0;;::::1;2700:778;;;2759:26;2788:11;;2800:1;2788:14;;;;;;;:::i;:::-;;;;;;2759:43;;2820:6;:27;;;2851:1;2820:32:::0;2816:46:::1;;2854:8;;;2816:46;3047:34;3057:23;;::::0;::::1;:6:::0;:23:::1;:::i;3047:34::-;3043:48;;;3083:8;;;3043:48;3106:21;3130:10;3106:21:::0;3141:23:::1;;::::0;::::1;:6:::0;:23:::1;:::i;:::-;-1:-1:-1::0;;;;;3130:35:204::1;::::0;;::::1;::::0;;::::1;::::0;;;;;;;;-1:-1:-1;3130:35:204;;;:48:::1;;::::0;-1:-1:-1;;;3130:48:204;::::1;;;3289:25:::0;;;:8:::1;:25:::0;;;;;:37;3130:48;;-1:-1:-1;3289:37:204;;3357:41:::1;::::0;3371:27;::::1;;3289:37:::0;3357:41:::1;:::i;:::-;3340:58:::0;-1:-1:-1;3413:21:204::1;3340:58:::0;3413:21;::::1;:::i;:::-;;;3461:6;3448:7;3456:1;3448:10;;;;;;;;:::i;:::-;;;;;;:19;;;::::0;::::1;2745:733;;;;2700:778;2740:3;;2700:778;;;;3492:11;3507:1;3492:16:::0;3488:29:::1;;3510:7;;2415:1583:::0;;:::o;3488:29::-:1;3600:21;3624;:19;:21::i;:::-;:35;;;3600:59;;3674:9;3669:323;3685:22:::0;;::::1;3669:323;;;3845:20;3899:11;3885:7;3893:1;3885:10;;;;;;;;:::i;:::-;;;;;;;3869:13;:26;;;;:::i;:::-;3868:42;;;;:::i;:::-;3845:65;;3969:12;3924:8;:41;3933:11;;3945:1;3933:14;;;;;;;:::i;:::-;:31;::::0;::::1;:14;::::0;;::::1;;:31:::0;;::::1;::::0;-1:-1:-1;3933:31:204::1;:::i;:::-;-1:-1:-1::0;;;;;3924:41:204::1;-1:-1:-1::0;;;;;3924:41:204::1;;;;;;;;;;;;;:57;;;;;;;:::i;:::-;::::0;;;-1:-1:-1;;;3709:3:204::1;;3669:323;;;;2506:1492;;;665:1:207;2415:1583:204::0;;:::o;9165:1579::-;1350:19:152;:17;:19::i;:::-;9409:2:204::1;9389:22:::0;::::1;9385:53;;9420:18;;-1:-1:-1::0;;;9420:18:204::1;;;;;;;;;;;9385:53;9547:22;9572:21;:19;:21::i;:::-;:34;;;9547:59;;9616:16;9635:45;9652:9;9663:16;9635;:45::i;:::-;9616:64;;9690:41;9714:16;9690:23;:41::i;:::-;;9793:66;9815:16;9833:25;9793:21;:66::i;:::-;-1:-1:-1::0;;;;;9884:29:204;::::1;9869:12;9884:29:::0;;;:11:::1;:29;::::0;;;;;:35:::1;;::::0;9953:20;;-1:-1:-1;;;9884:35:204;;::::1;-1:-1:-1::0;;;;;9884:35:204::1;::::0;9869:12;9953:20:::1;::::0;9963:9;;;;9953:20:::1;:::i;:::-;;;;;;;;9929:44;;10082:7;1710:6:121::0;;-1:-1:-1;;;;;1710:6:121;;;;;;1638:85;10082:7:204::1;-1:-1:-1::0;;;;;10068:21:204::1;:10;-1:-1:-1::0;;;;;10068:21:204::1;;10064:449;;10105:18;1295:139:206;10185:13:204;10200:16;10218:10;10230:16;10248:5;10153:101;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;10126:142;;;;;;10105:163;;10282:14;10299:26;10314:10;10299:14;:26::i;:::-;10282:43;;10344:87;10392:16;10410:6;10418:12;;10344:47;:87::i;:::-;10339:164;;10458:30;::::0;-1:-1:-1;;;10458:30:204;;-1:-1:-1;;;;;2816:32:226;;10458:30:204::1;::::0;::::1;2798:51:226::0;2771:18;;10458:30:204::1;2652:203:226::0;10339:164:204::1;10091:422;;10064:449;10567:84;;;;;;;;10578:13;10567:84;;;;10593:10;-1:-1:-1::0;;;;;10567:84:204::1;;;;;10605:16;-1:-1:-1::0;;;;;10567:84:204::1;;;;;10623:16;10567:84;;;;;;10641:5;10649:1;10641:9;;;;:::i;:::-;-1:-1:-1::0;;;;;10567:84:204;;::::1;::::0;;;-1:-1:-1;;;;;10523:29:204;;::::1;;::::0;;;:11:::1;:29;::::0;;;;;;;;:128;;;;;;::::1;::::0;::::1;::::0;;::::1;::::0;;;;::::1;-1:-1:-1::0;;;;;;10523:128:204;;::::1;::::0;;;::::1;::::0;;;;;::::1;::::0;::::1;::::0;;::::1;::::0;;::::1;::::0;::::1;::::0;::::1;::::0;;::::1;::::0;;;::::1;-1:-1:-1::0;;;10523:128:204::1;-1:-1:-1::0;;;;10523:128:204::1;::::0;;::::1;-1:-1:-1::0;;;10523:128:204::1;-1:-1:-1::0;;;;;;10523:128:204;;;;;;::::1;::::0;;;;;;;::::1;::::0;;;::::1;;::::0;;10661:76:::1;::::0;10675:9;;;;10535:16;;10710;10728:8;10661:13:::1;:76::i;:::-;9375:1369;;;;9165:1579:::0;;;;;:::o;10789:500::-;1350:19:152;:17;:19::i;:::-;10941:35:204::1;10965:10;10941:23;:35::i;:::-;;11032:57;11054:10;11066:22;11032:21;:57::i;:::-;11145:10;11100:31;11134:22:::0;;;:10:::1;:22;::::0;;;;11269:12:::1;::::0;11241:41:::1;::::0;11134:22;;-1:-1:-1;;;11269:12:204;::::1;;;11241:16;:41::i;:::-;10841:448;10789:500::o:0;6630:195::-;6699:20;;:::i;:::-;6731:41;6755:16;6731:23;:41::i;:::-;-1:-1:-1;;;;;;6790:28:204;;;;;;:10;:28;;;;;;;6783:35;;;;;;;;;;;;6790:28;;6783:35;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;6783:35:204;;;-1:-1:-1;;6783:35:204;;;;-1:-1:-1;;;;;6783:35:204;;;;;;;-1:-1:-1;;;6783:35:204;;;;;;;;-1:-1:-1;;;6783:35:204;;;;;;;;;;;;;-1:-1:-1;;;6783:35:204;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;6783:35:204;;;;;;;;;;-1:-1:-1;;;6783:35:204;;;;;;;;;;-1:-1:-1;;;6783:35:204;;;;;;;;;;6630:195;-1:-1:-1;;6630:195:204:o;12073:899::-;1350:19:152;:17;:19::i;:::-;12226:35:204::1;12250:10;12226:23;:35::i;:::-;;12318:17;12338:38;12353:22;12338:14;:38::i;:::-;:45:::0;12427:12:::1;::::0;12338:45;;-1:-1:-1;12393:21:204::1;::::0;12417:9:::1;::::0;-1:-1:-1;;;12427:12:204;::::1;;;12417:23;::::0;::::1;;;;;:::i;:::-;;;;:40:::0;;-1:-1:-1;12467:45:204::1;12487:9:::0;12417:40;12467:19:::1;:45::i;:::-;12606:57;12628:10;12640:22;12606:21;:57::i;:::-;12718:10;12673:31;12707:22:::0;;;:10:::1;:22;::::0;;;;12756:12:::1;::::0;12792:38:::1;::::0;::::1;::::0;12756:12:::1;-1:-1:-1::0;;;12756:12:204;;::::1;::::0;::::1;::::0;-1:-1:-1;;;12792:38:204;;::::1;;12782:48:::0;::::1;12778:107;;;12853:21;::::0;-1:-1:-1;;;12853:21:204;;16120:10:226;16108:23;;12853:21:204::1;::::0;::::1;16090:42:226::0;16063:18;;12853:21:204::1;15946:192:226::0;12778:107:204::1;12944:21;12955:9;12944:10;:21::i;:::-;12126:846;;;;12073:899::o:0;11328:700::-;1350:19:152;:17;:19::i;:::-;1635:9:187::1;1610:22;1604:29;1601:44:::0;1598:158:::1;;1677:10;1671:4;1664:24;1737:4;1731;1724:18;1598:158;1800:9;1776:22;1769:41;11529::204::2;11553:16;11529:23;:41::i;:::-;-1:-1:-1::0;;;;;;11605:28:204;::::2;11580:22;11605:28:::0;;;:10:::2;:28;::::0;;;;:41:::2;;::::0;-1:-1:-1;;;11605:41:204;::::2;;;::::0;11744:31:::2;11605:28:::0;11744:13:::2;:31::i;:::-;11724:51:::0;-1:-1:-1;11789:10:204::2;-1:-1:-1::0;;;;;11789:30:204;::::2;;::::0;::::2;::::0;:57:::2;;-1:-1:-1::0;11823:10:204::2;-1:-1:-1::0;;;;;11823:23:204;::::2;;;11789:57;11785:93;;;11855:23;::::0;-1:-1:-1;;;11855:23:204;;-1:-1:-1;;;;;2816:32:226;;11855:23:204::2;::::0;::::2;2798:51:226::0;2771:18;;11855:23:204::2;2652:203:226::0;11785:93:204::2;11888:15;11906:65;11925:16;11943:9;11954:16;11906:18;:65::i;:::-;11987:34;::::0;;-1:-1:-1;;;;;21179:32:226;;21161:51;;21243:2;21228:18;;21221:34;;;11888:83:204;;-1:-1:-1;11987:34:204::2;::::0;21134:18:226;11987:34:204::2;;;;;;;11426:602;;;1937:10:187::1;1913:22;1906:42;11328:700:204::0;:::o;8441:685::-;1350:19:152;:17;:19::i;:::-;8556:2:204::1;8536:22:::0;::::1;8532:53;;8567:18;;-1:-1:-1::0;;;8567:18:204::1;;;;;;;;;;;8532:53;8694:22;8719:21;:19;:21::i;:::-;:34;;;8694:59;;8763:16;8782:45;8799:9;8810:16;8782;:45::i;:::-;8763:64;;8837:35;8861:10;8837:23;:35::i;:::-;;8930:60;8952:10;8964:25;8930:21;:60::i;:::-;9048:71;9062:9;;9073:10;9085:5;9092:16;9110:8;9048:13;:71::i;1736:255:143:-:0;1824:7;1856:16;1866:5;1856:9;:16::i;:::-;1847:5;:25;1843:99;;1895:36;;-1:-1:-1;;;1895:36:143;;-1:-1:-1;;;;;21179:32:226;;1895:36:143;;;21161:51:226;21228:18;;;21221:34;;;21134:18;;1895:36:143;20987:274:226;1843:99:143;-1:-1:-1;;;;;;1958:19:143;;;;;;;;:12;:19;;;;;;;;:26;;;;;;;;;1736:255::o;15061:174:204:-;1531:13:121;:11;:13::i;:::-;15147:8:204::1;::::0;:37:::1;::::0;15136:6:::1;::::0;-1:-1:-1;;;;;15147:8:204::1;::::0;15169:9:::1;::::0;15136:6;15147:37;15136:6;15147:37;15169:9;15147:8;:37:::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;15135:49;;;15202:1;15194:34;;;::::0;-1:-1:-1;;;15194:34:204;;21678:2:226;15194:34:204::1;::::0;::::1;21660:21:226::0;21717:2;21697:18;;;21690:30;-1:-1:-1;;;21736:18:226;;;21729:50;21796:18;;15194:34:204::1;21476:344:226::0;5349:495:204;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5451:12:204;;-1:-1:-1;;;5451:12:204;;;;5485:11;5451:12;5495:1;5485:11;:::i;:::-;5477:19;;:5;:19;;;:60;;;;5512:1;5501:7;:12;;;;:35;;;;-1:-1:-1;5525:11:204;5535:1;5525:7;:11;:::i;:::-;5517:19;;:5;:19;;;5501:35;5473:117;;;5560:19;;-1:-1:-1;;;5560:19:204;;16120:10:226;16108:23;;5560:19:204;;;16090:42:226;16063:18;;5560:19:204;15946:192:226;5473:117:204;5623:12;;-1:-1:-1;;;5623:12:204;;;;5649:15;;;;;;;;5645:193;;;5687:51;5707:5;5714:7;5723:14;5687:19;:51::i;:::-;5680:58;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;5680:58:204;;;;;;;;;;;;;;;;-1:-1:-1;;;5680:58:204;;;-1:-1:-1;;5680:58:204;;;;;;;;;;;;;-1:-1:-1;;;;;5680:58:204;;;;;;-1:-1:-1;;;5680:58:204;;;;;;;;-1:-1:-1;;;5680:58:204;;;;;;;;;;;-1:-1:-1;;;;5349:495:204:o;5645:193::-;5776:51;5796:5;5803:7;5812:14;5776:19;:51::i;1143:29::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;1143:29:204;;;-1:-1:-1;;;1143:29:204;;;;;-1:-1:-1;;;1143:29:204;;;;;:::o;29402:65::-;1531:13:121;:11;:13::i;:::-;29450:10:204::1;:8;:10::i;:::-;29402:65::o:0;4786:132:140:-;4872:39;4889:4;4895:2;4899:7;4872:39;;;;;;;;;;;;:16;:39::i;2236:226:143:-;2302:7;2334:13;2140:10;:17;;2062:102;2334:13;2325:5;:22;2321:101;;2370:41;;-1:-1:-1;;;2370:41:143;;2401:1;2370:41;;;21161:51:226;21228:18;;;21221:34;;;21134:18;;2370:41:143;20987:274:226;2321:101:143;2438:10;2449:5;2438:17;;;;;;;;:::i;:::-;;;;;;;;;2431:24;;2236:226;;;:::o;1178:35:204:-;;;;;;;;;;;2184:118:140;2247:7;2273:22;2287:7;2273:13;:22::i;4838:129:204:-;4902:5;4926:21;:19;:21::i;:::-;:34;;;4919:41;;4838:129;:::o;14225:405::-;1531:13:121;:11;:13::i;:::-;14396:27:204::1;14406:16;14396:9;:27::i;:::-;:32:::0;::::1;::::0;:63:::1;;;14432:27;14442:16;14432:9;:27::i;:::-;14392:133;;;14482:32;::::0;-1:-1:-1;;;14482:32:204;;-1:-1:-1;;;;;2816:32:226;;14482::204::1;::::0;::::1;2798:51:226::0;2771:18;;14482:32:204::1;2652:203:226::0;14392:133:204::1;14569:54;14575:16:::0;-1:-1:-1;;;;;10945:32:206;;14569:5:204::1;:54::i;6870:477::-:0;6936:4;6956:38;-1:-1:-1;;;;;10945:32:206;;6956:7:204;:38::i;:::-;6952:333;;;-1:-1:-1;7073:5:204;;6870:477;-1:-1:-1;6870:477:204:o;6952:333::-;7145:25;-1:-1:-1;;;;;7099:28:204;;;;;;:10;:28;;;;;:42;;;-1:-1:-1;;;7099:42:204;;;;:71;;;;;;;;:::i;:::-;;7095:190;;-1:-1:-1;7269:5:204;;6870:477;-1:-1:-1;6870:477:204:o;7095:190::-;-1:-1:-1;;;;;;7302:28:204;;;;;:10;:28;;;;;:38;;;-1:-1:-1;;;7302:38:204;;;;;6870:477::o;1919:208:140:-;1982:7;-1:-1:-1;;;;;2005:19:140;;2001:87;;2047:30;;-1:-1:-1;;;2047:30:140;;2074:1;2047:30;;;2798:51:226;2771:18;;2047:30:140;2652:203:226;2001:87:140;-1:-1:-1;;;;;;2104:16:140;;;;;:9;:16;;;;;;;1919:208::o;2293:101:121:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;7386:292:204:-:0;-1:-1:-1;;;;;7502:28:204;;7462:7;7502:28;;;:10;:28;;;;;;;;:41;;;-1:-1:-1;;;7502:41:204;;;;7576:22;;;:8;:22;;;;;:34;7628:43;7502:28;7576:34;7628:11;:43::i;:::-;7621:50;7386:292;-1:-1:-1;;;;7386:292:204:o;1585:785::-;592:10:207;-1:-1:-1;;;;;592:28:207;588:67;;629:26;;-1:-1:-1;;;629:26:207;;644:10;629:26;;;2798:51:226;2771:18;;629:26:207;2652:203:226;588:67:207;1732:32:204::1;1748:15;;1732;:32::i;:::-;1834:15;1851:16:::0;1869:15:::1;1886:29:::0;1931:33:::1;1948:15;;1931:33;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;::::0;;;;-1:-1:-1;1931:16:204::1;::::0;-1:-1:-1;;;1931:33:204:i:1;:::-;1833:131;;;;;;;;1974:48;1996:15;;2013:8;1974:21;:48::i;:::-;2110:32;2145:38;2160:22;2145:14;:38::i;:::-;2213:16:::0;;2110:73;;-1:-1:-1;2193:61:204::1;::::0;2231:15;2193:19:::1;:61::i;:::-;2270:93;2279:83;;;;;;;;2289:12;2279:83;;;;2303:8;2279:83;;;;2320:12;2335:1;2320:16;;;;:::i;:::-;-1:-1:-1::0;;;;;2279:83:204::1;::::0;;::::1;::::0;::::1;;::::0;::::1;::::0;2349:12:::1;::::0;-1:-1:-1;;;2349:12:204;::::1;;;2279:83:::0;;;;;2270:93;::::1;::::0;;::::1;:::i;:::-;;;;;;;;1677:693;;;;;1585:785:::0;;:::o;3945:120:206:-;3999:18;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3999:18:206;-1:-1:-1;4045:12:206;;-1:-1:-1;;;4045:12:206;;;;4036:22;;;;:8;:22;;;;;;;;;4029:29;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3945:120::o;29165:61:204:-;1531:13:121;:11;:13::i;:::-;29211:8:204::1;:6;:8::i;6989:596:185:-:0;-1:-1:-1;;;7114:18:185;;7087:13;;;7114:18;7385:23;11450:36:206;;;;;;;;;;;-1:-1:-1;;;11450:36:206;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;11450:36:206;;;;;11337:156;7385:23:185;6989:596;;7367:41;;;-1:-1:-1;7428:13:185;;-1:-1:-1;7479:4:185;;-1:-1:-1;6989:596:185;;-1:-1:-1;6989:596:185;:::o;14669:353:204:-;1531:13:121;:11;:13::i;:::-;14751:27:204::1;14761:16;14751:9;:27::i;:::-;14747:74;;;14801:19;14787:34;;-1:-1:-1::0;;;14787:34:204::1;;;;;;;;:::i;14747:74::-;14931:41;14955:16;14931:23;:41::i;:::-;;14983:32;14998:16;14983:14;:32::i;5889:218::-:0;5957:22;6005:25;5995:6;:35;;;;;;;;:::i;:::-;;5991:69;;6053:6;6039:21;;-1:-1:-1;;;6039:21:204;;;;;;;;:::i;5991:69::-;6078:22;6093:6;6078:14;:22::i;2517:93:140:-;2564:13;2596:7;2589:14;;;;;:::i;6152:433:204:-;6219:22;6253:26;6282:19;6295:5;6282:12;:19::i;:::-;:29;;;6253:58;;6321:42;6386:9;:16;-1:-1:-1;;;;;6366:37:204;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;6321:82;;6418:9;6413:129;6433:19;:26;6429:1;:30;6413:129;;;6505:26;6518:9;6528:1;6518:12;;;;;;;;:::i;:::-;;;;;;;6505;:26::i;:::-;6480:19;6500:1;6480:22;;;;;;;;:::i;:::-;;;;;;;;;;:51;6461:3;;6413:129;;;-1:-1:-1;6559:19:204;6152:433;-1:-1:-1;;;6152:433:204:o;3779:126:206:-;3844:18;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3844:18:206;-1:-1:-1;3881:17:206;;;;;;:8;:17;;;;;;;;;3874:24;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3779:126::o;4984:233:140:-;5097:31;5110:4;5116:2;5120:7;5097:12;:31::i;:::-;5138:72;735:10:149;5186:4:140;5192:2;5196:7;5205:4;5138:33;:72::i;5149:155:204:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5256:12:204;;5236:61;;-1:-1:-1;;;5256:12:204;;;;;;;-1:-1:-1;;;5284:12:204;;;;5236:19;:61::i;:::-;5229:68;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;5229:68:204;;;;;;;;;;;;;;;;-1:-1:-1;;;5229:68:204;;;-1:-1:-1;;5229:68:204;;;;;;;;;;;;;-1:-1:-1;;;;;5229:68:204;;;;;;-1:-1:-1;;;5229:68:204;;;;;;;;-1:-1:-1;;;5229:68:204;;;;;;;;;;;-1:-1:-1;5149:155:204:o;33014:375::-;33166:5;1531:13:121;:11;:13::i;:::-;1350:19:152::1;:17;:19::i;:::-;33191:23:204::2;::::0;;;::::2;::::0;::::2;;:::i;:::-;:28;;33218:1;33191:28:::0;33187:81:::2;;33244:23;::::0;;;::::2;::::0;::::2;;:::i;:::-;33228:40;::::0;-1:-1:-1;;;33228:40:204;;16120:10:226;16108:23;;;33228:40:204::2;::::0;::::2;16090:42:226::0;16063:18;;33228:40:204::2;15946:192:226::0;33187:81:204::2;33279:16;33300:12;;33298:14;;;;;;;;;;;;;;:::i;:::-;::::0;;::::2;::::0;;::::2;;::::0;;;::::2;::::0;;::::2;::::0;::::2;;::::0;;::::2;;::::0;;;-1:-1:-1;33322:20:204;;;:8:::2;:20;::::0;;;;33298:14;;-1:-1:-1;33345:9:204;;33322:32:::2;33345:9:::0;33322:20;:32:::2;:::i;:::-;-1:-1:-1::0;33372:10:204;;-1:-1:-1;;1379:1:152::2;33014:375:204::0;;;:::o;6215:672:206:-;6288:13;6313:22;6327:7;6313:13;:22::i;:::-;;6346:18;6367:443;6514:54;6558:7;6514:27;:54::i;:::-;6732:10;:8;:10::i;:::-;6417:369;;;;;;;;;:::i;:::-;;;;;;;;;;;;;6367:13;:443::i;:::-;6346:464;;6875:4;6828:52;;;;;;;;:::i;:::-;;;;;;;;;;;;;6821:59;;;6215:672;;;:::o;7718:633:204:-;7907:7;7930:41;7954:16;7930:23;:41::i;:::-;;7981:18;8002:21;:19;:21::i;:::-;:34;;;-1:-1:-1;;;;;8061:29:204;;8046:12;8061:29;;;:11;:29;;;;;;;;:35;;;8130:20;;;;;;8203:96;;8002:34;;-1:-1:-1;;;;8061:35:204;;-1:-1:-1;;;;;8061:35:204;;8130:20;;8046:12;8203:96;;1295:139:206;;8130:20:204;;8061:29;;8268:9;;8002:34;;8061:35;;8203:96;;:::i;:::-;;;;;;;;;;;;;8193:107;;;;;;8160:140;;8318:26;8333:10;8318:14;:26::i;:::-;8311:33;;;;;;7718:633;;;;;;:::o;3927:153:140:-;-1:-1:-1;;;;;4038:25:140;;;4015:4;4038:25;;;:18;:25;;;;;;;;:35;;;;;;;;;;;;;;;3927:153::o;13011:1128:204:-;1350:19:152;:17;:19::i;:::-;1635:9:187::1;1610:22;1604:29;1601:44:::0;1598:158:::1;;1677:10;1671:4;1664:24;1737:4;1731;1724:18;1598:158;1800:9;1776:22;1769:41;13202::204::2;13226:16;13202:23;:41::i;:::-;;13321:17;13341:31;13355:16;13341:13;:31::i;:::-;13321:51:::0;-1:-1:-1;13386:10:204::2;-1:-1:-1::0;;;;;13386:30:204;::::2;;::::0;::::2;::::0;:57:::2;;-1:-1:-1::0;13420:10:204::2;-1:-1:-1::0;;;;;13420:23:204;::::2;;;13386:57;13382:93;;;13452:23;::::0;-1:-1:-1;;;13452:23:204;;-1:-1:-1;;;;;2816:32:226;;13452:23:204::2;::::0;::::2;2798:51:226::0;2771:18;;13452:23:204::2;2652:203:226::0;13382:93:204::2;-1:-1:-1::0;;;;;13594:28:204;::::2;13569:22;13594:28:::0;;;:10:::2;:28;::::0;;;;:42:::2;::::0;;::::2;::::0;-1:-1:-1;;;13594:42:204;::::2;;;::::0;13650:6:::2;:32;;;;;;;;:::i;:::-;;;:68;;;;-1:-1:-1::0;13696:22:204::2;13686:6;:32;;;;;;;;:::i;:::-;;;13650:68;13646:102;;;13741:6;13727:21;;-1:-1:-1::0;;;13727:21:204::2;;;;;;;;:::i;13646:102::-;-1:-1:-1::0;;;;;13863:28:204;::::2;13829:31;13863:28:::0;;;:10:::2;:28;::::0;;;;13901:18:::2;13863:28:::0;13901:7:::2;:18::i;:::-;13987:23;14013:61;14022:16;14040:9;14051;:22;;;;;;;;;;;;14013:8;:61::i;:::-;14090:42;::::0;;-1:-1:-1;;;;;21179:32:226;;21161:51;;21243:2;21228:18;;21221:34;;;13987:87:204;;-1:-1:-1;14090:42:204::2;::::0;21134:18:226;14090:42:204::2;;;;;;;13099:1040;;;;1937:10:187::1;1913:22;1906:42;13011:1128:204::0;:::o;2543:215:121:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:121;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:121;;2700:1:::1;2672:31;::::0;::::1;2798:51:226::0;2771:18;;2672:31:121::1;2652:203:226::0;2623:91:121::1;2723:28;2742:8;2723:18;:28::i;1219:51:204:-:0;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;1219:51:204;;;;;;;-1:-1:-1;;;;;;;1219:51:204;;;;-1:-1:-1;;;1219:51:204;;;;;-1:-1:-1;;;1219:51:204;;;;;;;-1:-1:-1;;;1219:51:204;;;;;-1:-1:-1;;;1219:51:204;;;;;-1:-1:-1;;;1219:51:204;;;;;-1:-1:-1;;;1219:51:204;;;;:::o;1560:300:140:-;1662:4;-1:-1:-1;;;;;;1697:40:140;;-1:-1:-1;;;1697:40:140;;:104;;-1:-1:-1;;;;;;;1753:48:140;;-1:-1:-1;;;1753:48:140;1697:104;:156;;;-1:-1:-1;;;;;;;;;;862:40:157;;;1817:36:140;763:146:157;21977:947:204;-1:-1:-1;;;;;22046:26:204;;22075:1;22046:26;;;:8;:26;;;;;;;;:30;;;22121:10;:28;;;;;22184:23;;;;22121:28;;-1:-1:-1;;;22184:23:204;;;;;;22338:38;22353:22;22338:14;:38::i;:::-;:45;22316:67;;22496:38;22527:6;22496:30;:38::i;:::-;22492:98;;;22564:15;22578:1;22564:11;:15;:::i;:::-;22550:29;;22492:98;22599:51;22620:16;22638:11;22599:20;:51::i;:::-;22744:12;;22727:30;;22733:9;;-1:-1:-1;;;22744:12:204;;;;22727:5;:30::i;:::-;22767:18;22775:9;22767:7;:18::i;:::-;22795:17;22815:31;22829:16;22815:13;:31::i;:::-;22795:51;;22856:61;22865:16;22883:9;22894;:22;;;;;;;;;;;;22856:8;:61::i;16212:241:140:-;16275:7;5824:16;;;:7;:16;;;;;;-1:-1:-1;;;;;5824:16:140;;16337:88;;16383:31;;-1:-1:-1;;;16383:31:140;;;;;5119:25:226;;;5092:18;;16383:31:140;4973:177:226;1878:128:152;1796:7;;;;1939:61;;;1974:15;;-1:-1:-1;;;1974:15:152;;;;;;;;;;;9420:216:206;9534:17;;;9502:7;9534:17;;;:8;:17;;;;;:29;9525:38;;9521:76;;9572:25;;-1:-1:-1;;;9572:25:206;;;;;5119::226;;;5092:18;;9572:25:206;4973:177:226;9521:76:206;-1:-1:-1;9623:5:206;;9420:216;-1:-1:-1;9420:216:206:o;10526:324::-;10608:7;-1:-1:-1;;;;;10945:32:206;;10689:16;10945:32;10689:7;:16::i;:::-;10684:53;;10714:23;;-1:-1:-1;;;10714:23:206;;;;;5119:25:226;;;5092:18;;10714:23:206;4973:177:226;10684:53:206;10771:16;-1:-1:-1;;;;;10751:36:206;:16;10759:7;10751;:16::i;:::-;-1:-1:-1;;;;;10751:36:206;;10747:71;;10796:22;;-1:-1:-1;;;10796:22:206;;;;;;;;;;;26005:254:204;-1:-1:-1;;;;;26142:28:204;;26117:22;26142:28;;;:10;:28;;;;;:42;;;-1:-1:-1;;;26142:42:204;;;;26208:14;26198:24;;;;;;;;:::i;:::-;:6;:24;;;;;;;;:::i;:::-;;26194:58;;26245:6;26231:21;;-1:-1:-1;;;26231:21:204;;;;;;;;:::i;5757:885:185:-;6066:22;9114:11;9255:9;9252:25;9069:14;9225:9;9222:28;9218:60;6102:73;;-1:-1:-1;8545:4:185;8539:11;;8606:16;8596:27;;8383:15;8650:4;8643:12;;8636:31;8426:18;8701:12;;;8694:33;;;;8761:9;8754:4;8747:12;;8740:31;8805:9;8798:4;8791:12;;8784:31;8854:4;8841:18;;6102:73;6309:18;6303:4;6296:32;6375:6;6369:4;6362:20;6439:10;6433:4;6426:24;6515:4;6509;6499:21;6489:31;;6624:1;6618:4;6611:15;5757:885;;;:::o;6365:4213:188:-;-1:-1:-1;;;;;6690:24:188;;;;6505:12;6717:6;6674:3888;;;6760:4;6754:11;6795:4;6789;6782:18;6841:2;6823:16;6820:24;6817:1164;;6912:4;6890:27;;;6877:41;6960:3;6956:12;;;6970:2;6952:21;6939:35;;7016:30;;7010:4;7003:44;-1:-1:-1;;;;;7089:18:188;7083:4;7076:32;7100:1;7399:4;7346;7100:1;7210:5;7170:384;7725:1;7719:8;7711:6;7707:21;7688:16;7681:24;7678:51;7668:295;;-1:-1:-1;7807:1:188;7801:4;7794:15;7867:4;7860:15;-1:-1:-1;7768:1:188;7936:5;;7668:295;;6817:1164;8022:2;8004:16;8001:24;7998:1063;;8104:4;8086:16;8082:27;8069:41;8066:1;8061:50;8055:4;8048:64;8178:4;8160:16;8154:4;8141:42;8585:4;8531;8479;8426;8368:1;8290:5;8250:384;8805:1;8799:8;8791:6;8787:21;8768:16;8761:24;8758:51;8748:295;;-1:-1:-1;8887:1:188;8881:4;8874:15;8947:4;8940:15;-1:-1:-1;8848:1:188;9016:5;;8748:295;;7998:1063;9091:1;9085:4;9078:15;9149:1;9143:4;9136:15;9223:10;9218:3;9214:20;9261:1;9258;9251:12;9360:4;9353;9350:1;9346:12;9339:26;9398:4;9395:1;9391:12;9430:4;9427:1;9420:15;9523:16;9516:4;9513:1;9509:12;9502:38;9648:16;9630;9623:4;9620:1;9616:12;9603:62;10448:4;10396:1;10332:4;10314:16;10310:27;10250:1;10193:6;10144:5;10108:400;9862:8;;9859:15;;;9740:786;;-1:-1:-1;;6365:4213:188;;;;;;:::o;15438:801:204:-;15646:21;15680:9;;15670:20;;;;;;;:::i;:::-;;;;;;;;;;;15704:29;;;;:14;:29;;;;;;;15670:20;;-1:-1:-1;15704:29:204;;15700:62;;;15742:20;;-1:-1:-1;;;15742:20:204;;;;;;;;;;;15700:62;15772:29;;;;:14;:29;;;;;;;;:36;;-1:-1:-1;;15772:36:204;15804:4;15772:36;;;15855:232;;;;;;;;;;;;;;;;;;;15772:36;15855:232;;;;;;;;;;15882:9;;;;;;15855:232;;15882:9;;;;15855:232;;;;;;;;-1:-1:-1;15855:232:204;;;-1:-1:-1;;;;;;;;15855:232:204;;;;;;1473:16;15855:232;;;;;;;;;;15985:22;15855:232;;16021:5;15855:232;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;16097:28:204;;;;:10;:28;;;:43;;15819:268;;-1:-1:-1;15819:268:204;;16097:28;;:43;;:28;:43;:::i;:::-;-1:-1:-1;16097:43:204;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;16097:43:204;-1:-1:-1;;;;16097:43:204;;;;-1:-1:-1;;;16097:43:204;-1:-1:-1;;;;;;16097:43:204;;;-1:-1:-1;;;;;16097:43:204;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;16097:43:204;;;-1:-1:-1;;;;16097:43:204;;;;-1:-1:-1;;;16097:43:204;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;16097:43:204;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;16097:43:204;-1:-1:-1;;;;;16097:43:204;;;-1:-1:-1;;;16097:43:204;-1:-1:-1;;;;16097:43:204;;;-1:-1:-1;;;16097:43:204;;;;;-1:-1:-1;;;;16097:43:204;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;16150:26:204;;-1:-1:-1;16150:26:204;;;:8;:26;;;;;;;:37;;;16203:29;;;;;16219:12;;16203:29;:::i;:::-;;;;;;;;15636:603;;15438:801;;;;;;:::o;16340:258::-;16460:9;:5;16468:1;16460:9;:::i;:::-;16432:25;;;:37;;;;;;;-1:-1:-1;;;16432:37:204;-1:-1:-1;;;;16479:59:204;-1:-1:-1;;;;16479:59:204;;;;;;;-1:-1:-1;;;16479:59:204;;;16554:37;;;;;;16432:25;;16554:37;:::i;:::-;;;;;;;;16340:258;;:::o;27507:1433::-;27578:22;27612:40;27676:13;2140:10:143;:17;;2062:102;27676:13:204;-1:-1:-1;;;;;27655:36:204;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;27612:79;;27701:18;27735:9;27730:1056;27750:17;:24;27746:1;:28;27730:1056;;;27795:24;27822:28;27834:15;27847:1;27834:12;:15::i;:::-;11093:7:206;10990:119;27822:28:204;-1:-1:-1;;;;;27896:28:204;;27864:29;27896:28;;;:10;:28;;;;;27942:17;;;;27896:28;;-1:-1:-1;27896:28:204;-1:-1:-1;;;27942:17:204;;;;27938:31;;;27961:8;;;;27938:31;28057:15;28085:19;28075:6;:29;;;;;;;;:::i;:::-;;28057:47;;28123:10;28118:554;;28229:21;;;;-1:-1:-1;;;28229:21:204;;;;28385:22;28375:6;:32;;;;;;;;:::i;:::-;;28371:287;;28444:45;28475:13;28444:30;:45::i;:::-;28431:58;;28371:287;;;28633:6;28616:23;;;;;;;;:::i;:::-;:13;:23;;;;;;;;:::i;:::-;;28603:36;;28371:287;28135:537;28118:554;28690:10;28686:90;;;28754:7;28720:41;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;28720:41:204;;;-1:-1:-1;;28720:41:204;;;;-1:-1:-1;;;;;28720:41:204;;;;;;;-1:-1:-1;;;28720:41:204;;;;;;;;-1:-1:-1;;;28720:41:204;;;;;;;;;;;;;-1:-1:-1;;;28720:41:204;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;28720:41:204;;;;;;;;;;-1:-1:-1;;;28720:41:204;;;;;;;;;;-1:-1:-1;;;28720:41:204;;;;;;;;;:17;28738:12;;;;:::i;:::-;;;28720:31;;;;;;;;:::i;:::-;;;;;;:41;;;;28686:90;27781:1005;;;27730:1056;27776:3;;27730:1056;;;-1:-1:-1;28852:37:204;;28859:17;27507:1433;-1:-1:-1;;27507:1433:204:o;25625:274::-;25730:20;;;:42;;-1:-1:-1;25754:18:204;;25730:42;:77;;;;25792:15;25776:13;:31;25730:77;25726:167;;;25830:52;;-1:-1:-1;;;25830:52:204;;;;;29196:25:226;;;29237:18;;;29230:34;;;29169:18;;25830:52:204;29022:248:226;17061:224:204;17133:23;;;:53;;-1:-1:-1;;;;17196:35:204;-1:-1:-1;;;17196:35:204;;;17247:31;;;;;;17133:23;;17247:31;:::i;:::-;;;;;;;;17061:224;:::o;10098:305:206:-;-1:-1:-1;;;;;10221:29:206;;;10170:7;10221:29;;;:11;:29;;;;;10280:20;;;;10170:7;;10221:29;;10280:20;;10310:59;;-1:-1:-1;10353:16:206;;10387:9;-1:-1:-1;;10098:305:206:o;7117:490::-;7294:7;7391:15;7409:49;7423:16;7441;7409:13;:49::i;:::-;-1:-1:-1;;;;;7468:26:206;;;;;;:8;:26;;;;;:37;;7391:67;;-1:-1:-1;7391:67:206;;7468:26;;;:37;;7391:67;;7468:37;:::i;:::-;;;;-1:-1:-1;;7524:8:206;;7515:60;;-1:-1:-1;;;7515:60:206;;-1:-1:-1;;;;;21179:32:226;;;7515:60:206;;;21161:51:226;21228:18;;;21221:34;;;7524:8:206;;;;7515:40;;21134:18:226;;7515:60:206;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;7593:7:206;;7117:490;-1:-1:-1;;;;;;;7117:490:206:o;1796:162:121:-;1710:6;;-1:-1:-1;;;;;1710:6:121;;;;;735:10:149;1855:23:121;1851:101;;1901:40;;-1:-1:-1;;;1901:40:121;;735:10:149;1901:40:121;;;2798:51:226;2771:18;;1901:40:121;2652:203:226;24309:314:204;24468:17;;24568:1;24550:14;24530:16;24539:7;24530:6;:16;:::i;:::-;24524:40;;;;:::i;:::-;24523:46;;;;:::i;:::-;24501:68;;24586:15;24602:13;24586:30;;;;;;;;;:::i;:::-;;;;;24309:314;-1:-1:-1;;;;;24309:314:204:o;24733:437::-;24892:17;;25061:6;25047:11;25051:7;25047:1;:11;:::i;:::-;:20;;;;:::i;:::-;25021:47;-1:-1:-1;25078:13:204;25127:1;25095:28;25021:47;25095:14;:28;:::i;:::-;25094:34;;;;:::i;:::-;25078:50;;25145:9;25155:7;25145:18;;;;;;;;;:::i;:::-;;;;;24733:437;-1:-1:-1;;;;;;24733:437:204:o;2586:117:152:-;1597:16;:14;:16::i;:::-;2644:7:::1;:15:::0;;-1:-1:-1;;2644:15:152::1;::::0;;2674:22:::1;735:10:149::0;2683:12:152::1;2674:22;::::0;-1:-1:-1;;;;;2816:32:226;;;2798:51;;2786:2;2771:18;2674:22:152::1;;;;;;;2586:117::o:0;9978:327:140:-;-1:-1:-1;;;;;10045:16:140;;10041:87;;10084:33;;-1:-1:-1;;;10084:33:140;;10114:1;10084:33;;;2798:51:226;2771:18;;10084:33:140;2652:203:226;10041:87:140;10137:21;10161:32;10169:2;10173:7;10190:1;10161:7;:32::i;:::-;10137:56;-1:-1:-1;;;;;;10207:27:140;;;10203:96;;10257:31;;-1:-1:-1;;;10257:31:140;;10285:1;10257:31;;;2798:51:226;2771:18;;10257:31:140;2652:203:226;11115:216:206;11180:4;11200:12;;;:44;;-1:-1:-1;;;;;;11216:28:206;;;11200:44;11196:80;;;11253:23;;-1:-1:-1;;;11253:23:206;;;;;5119:25:226;;;5092:18;;11253:23:206;4973:177:226;11196:80:206;-1:-1:-1;11322:1:206;5824:16:140;;;:7;:16;;;;;;-1:-1:-1;;;;;5824:16:140;11293:31:206;;;11115:216::o;2912:187:121:-;3004:6;;;-1:-1:-1;;;;;3020:17:121;;;3004:6;3020:17;;;-1:-1:-1;;;;;;3020:17:121;;;;;;3052:40;;3004:6;;;;;;;;3052:40;;2985:16;;3052:40;2975:124;2912:187;:::o;9642:274:206:-;-1:-1:-1;;;;;9779:26:206;;9742:7;9779:26;;;:8;:26;;;;;;9742:7;9833:22;;;:51;;9883:1;9833:51;;;9858:22;9868:12;9858:7;:22;:::i;:::-;9815:69;9642:274;-1:-1:-1;;;;;9642:274:206:o;25176:266:204:-;25266:9;25261:175;25281:26;25306:1;25281:15;:26;:::i;:::-;25277:1;:30;25261:175;;;25354:15;;25370:5;:1;25374;25370:5;:::i;:::-;25354:22;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;25332:44:204;:15;;25348:1;25332:18;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;25332:44:204;;25328:97;;25406:15;;25422:1;25406:18;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;25385:40;;-1:-1:-1;;;25385:40:204;;-1:-1:-1;;;;;2816:32:226;;;25385:40:204;;;2798:51:226;2771:18;;25385:40:204;2652:203:226;25328:97:204;25309:3;;25261:175;;23041:1107;23273:12;;23135:6;;;;;;23160:16;;-1:-1:-1;;;23273:12:204;;;;23135:6;23344:1;23320:20;23273:12;23339:1;23320:20;:::i;:::-;23319:26;;;;:::i;:::-;23295:50;;23397:30;23430:15;23446;23430:32;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;23430:42:204;23518:23;:21;:23::i;:::-;23482:59;;23580:193;;;;;;;;23603:12;23580:193;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;23580:193:204;;;;;;;;;;;;;;;;-1:-1:-1;;;23580:193:204;;;-1:-1:-1;;23629:28:204;;;;;23580:193;;;;;23671:24;23678:12;23694:1;23671:24;:::i;:::-;-1:-1:-1;;;;;23580:193:204;;;23709:28;;;;23580:193;;;;;;23751:12;;;-1:-1:-1;;;23751:12:204;;;;;-1:-1:-1;23580:193:204;;;;;;;23551:9;;:26;;;;;;;;;:::i;:::-;;;;:222;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;23551:222:204;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;23551:222:204;;;-1:-1:-1;;23551:222:204;;;;-1:-1:-1;;;23551:222:204;;;;;;-1:-1:-1;;;;23551:222:204;-1:-1:-1;;;23551:222:204;;;;;;;;23783:12;:30;;-1:-1:-1;;;;23783:30:204;-1:-1:-1;;;23783:30:204;;;;;;;;;;;;;;-1:-1:-1;;23843:12:204;;23841:14;;-1:-1:-1;;;23841:14:204;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;23823:32;;23902:30;23959:1;23936:15;23954:1;23936:19;;;;:::i;:::-;23935:25;;;;:::i;:::-;23902:58;;24024:15;23970;23986:24;23970:41;;;;;;;;;:::i;:::-;;;;:51;;:69;;;;;;;;;;;;:::i;:::-;;24058:8;24068:14;:28;;;24098:14;:28;;;24128:12;24050:91;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;24050:91:204;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;23041:1107;;;;;:::o;18712:1396::-;18814:40;18857:49;18872:33;18857:14;:49::i;:::-;18814:92;;18921:9;18916:210;18936:17;:24;18932:1;:28;18916:210;;;18981:39;19023:10;:49;19034:17;19052:1;19034:20;;;;;;;;:::i;:::-;;;;;;;:37;;;-1:-1:-1;;;;;19023:49:204;-1:-1:-1;;;;;19023:49:204;;;;;;;;;;;;18981:91;;19087:28;19097:17;19087:9;:28::i;:::-;-1:-1:-1;18962:3:204;;18916:210;;;;19136:34;19173:43;19188:27;19173:14;:43::i;:::-;19254:12;;19136:80;;-1:-1:-1;;;;19254:12:204;;;;19226:25;19329:1;19302:23;19254:12;19324:1;19302:23;:::i;:::-;19301:29;;;;:::i;:::-;19276:54;;19340:33;19376:9;19386:19;19376:30;;;;;;;;;:::i;:::-;;;;19340:76;;;;;;;;;;;;;;;;;;;;19376:30;19340:76;;;19376:30;19340:76;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;19340:76:204;;;;;;;;;;;;;;;;;;;;;;;19426:30;19459:15;19475:16;19459:33;;;;;;;;;:::i;:::-;;;;19426:76;;;;;;;;;;;;;;;;;;;;19459:33;19426:76;;;19459:33;19426:76;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;19426:76:204;;;;;;;;;;;;;;;;;;;;;;;19517:9;19512:590;19532:11;:18;19528:1;:22;19512:590;;;19645:24;19672:11;19684:1;19672:14;;;;;;;;:::i;:::-;;;;;;;:31;;;19645:58;;19738:54;19757:16;19775;19738:18;:54::i;:::-;:129;;;;19816:51;19835:16;19853:13;19816:18;:51::i;:::-;19738:206;;;;19891:53;19910:16;19928:15;;19891:53;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;19891:18:204;;-1:-1:-1;;;19891:53:204:i;:::-;19717:250;;;19959:8;;;19717:250;-1:-1:-1;;;;;20020:28:204;;19982:35;20020:28;;;:10;:28;;;;;20062:29;20020:28;20083:7;20062:5;:29::i;:::-;19557:545;;19512:590;19552:3;;19512:590;;;;18804:1304;;;;;;18712:1396;;;:::o;2339:115:152:-;1350:19;:17;:19::i;:::-;2398:7:::1;:14:::0;;-1:-1:-1;;2398:14:152::1;2408:4;2398:14;::::0;;2427:20:::1;2434:12;735:10:149::0;;656:96;993:924:146;-1:-1:-1;;;;;1173:14:146;;;:18;1169:742;;1211:67;;-1:-1:-1;;;1211:67:146;;-1:-1:-1;;;;;1211:36:146;;;;;:67;;1248:8;;1258:4;;1264:7;;1273:4;;1211:67;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1211:67:146;;;;;;;;-1:-1:-1;;1211:67:146;;;;;;;;;;;;:::i;:::-;;;1207:694;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1568:6;:13;1585:1;1568:18;1564:323;;1672:39;;-1:-1:-1;;;1672:39:146;;-1:-1:-1;;;;;2816:32:226;;1672:39:146;;;2798:51:226;2771:18;;1672:39:146;2652:203:226;1564:323:146;1839:6;1833:13;1824:6;1820:2;1816:15;1809:38;1207:694;-1:-1:-1;;;;;;1325:51:146;;-1:-1:-1;;;1325:51:146;1321:182;;1445:39;;-1:-1:-1;;;1445:39:146;;-1:-1:-1;;;;;2816:32:226;;1445:39:146;;;2798:51:226;2771:18;;1445:39:146;2652:203:226;1207:694:146;993:924;;;;;:::o;3618:723:154:-;3684:13;3709:19;3737:17;3749:4;3737:11;:17::i;:::-;3956:2;3949:4;3937:17;;3927:32;3709:46;;-1:-1:-1;3923:2:154;3919:41;3997:2;3980:324;4005:1;4001;:5;3980:324;;;4137:1;4119:9;4131:3;4119:15;:19;:44;;;;;4161:2;4148:6;4155:1;4148:9;;;;;;;;:::i;:::-;;;;;;;4142:21;4119:44;4115:150;;;4246:4;4233:17;;:6;4240:1;4233:9;;;;;;;;:::i;:::-;;;;:17;;-1:-1:-1;;;;;;4233:17:154;;;;;;;;;;;;;;;;;4115:150;4292:1;4278:15;;;;;4008:3;;;:::i;:::-;;;3980:324;;;-1:-1:-1;4327:6:154;;3618:723;-1:-1:-1;;;3618:723:154:o;6893:169:206:-;6953:13;7029:25;7049:3;;;;;;;;;;;;;;;;;7029:13;:25::i;:::-;6985:70;;;;;;;;:::i;:::-;;;;;;;;;;;;;6978:77;;6893:169;:::o;663:124:148:-;721:13;753:27;761:4;767:6;;;;;;;;;;;;;;;;;775:4;753:7;:27::i;18024:200:204:-;18093:23;;;:45;;-1:-1:-1;;;;18148:26:204;-1:-1:-1;;;18148:26:204;;;18190:27;;;;;;18093:23;;18190:27;:::i;7613:1367:206:-;7822:26;;;7780:7;7822:26;;;:8;:26;;;;;:38;7780:7;7888:39;7900:16;7822:38;7888:11;:39::i;:::-;-1:-1:-1;;;;;8004:26:206;;7990:11;8004:26;;;:8;:26;;;;;;;8040:30;;;7870:57;;-1:-1:-1;8080:36:206;;:5;:36::i;:::-;2140:10:143;:17;8147:1:206;8130:18;8126:46;;8157:15;;-1:-1:-1;;;8157:15:206;;;;;;;;;;;8126:46;8258:18;8297:8;8290:3;:15;8286:450;;-1:-1:-1;8428:8:206;8286:450;;;-1:-1:-1;8668:8:206;;8577:3;;8657:6;;-1:-1:-1;;;;;8668:8:206;8690:14;8577:3;8690:8;:14;:::i;:::-;8668:42;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;8286:450:206;8863:8;;8854:81;;-1:-1:-1;;;8854:81:206;;-1:-1:-1;;;;;21179:32:226;;;8854:81:206;;;21161:51:226;21228:18;;;21221:34;;;8863:8:206;;;;8854:40;;8903:10;;21134:18:226;;8854:81:206;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8966:7;8953:10;:20;;;;:::i;26883:277:204:-;26970:4;27017:22;27007:6;:32;;;;;;;;:::i;:::-;;:73;;;-1:-1:-1;27053:27:204;27043:6;:37;;;;;;;;:::i;:::-;;27007:73;:136;;;-1:-1:-1;27110:33:204;27100:6;:43;;;;;;;;:::i;:::-;;26986:167;26883:277;-1:-1:-1;;26883:277:204:o;20297:1274::-;20410:12;;-1:-1:-1;;;20410:12:204;;;;;-1:-1:-1;;;20460:12:204;;;;20393:14;20519:58;20410:12;;20460;20519:19;:58::i;:::-;:68;-1:-1:-1;20519:68:204;20612:42;20519:68;20637:16;20612:6;:42::i;:::-;20597:57;;20664:21;20688:7;:63;;20728:23;;20688:63;;;20698:23;;:27;;20724:1;;20698:27;:::i;:::-;20664:87;;20761:47;20781:11;20794:13;20761:19;:47::i;:::-;20819:16;20838:11;:7;20848:1;20838:11;:::i;:::-;20819:30;;20859:31;20893:60;20913:9;20924:7;20933:19;20893;:60::i;:::-;:70;-1:-1:-1;20983:39:204;20893:70;21005:16;20983:6;:39::i;:::-;20973:49;;21048:7;:57;;21085:20;;21048:57;;;21058:20;;:24;;21081:1;;21058:24;:::i;:::-;21032:73;;21115:47;21135:11;21148:13;21115:19;:47::i;:::-;21173:22;21198:11;:7;21208:1;21198:11;:::i;:::-;21173:36;;21219:37;21271:66;21291:15;21308:7;21317:19;21271;:66::i;:::-;:76;-1:-1:-1;21367:45:204;21271:76;21395:16;21367:6;:45::i;:::-;21357:55;;21438:7;:69;;21481:26;;21438:69;;;21448:26;;:30;;21477:1;;21448:30;:::i;:::-;21422:85;;21517:47;21537:11;21550:13;21517:19;:47::i;:::-;20383:1188;;;;;;;;;20297:1274;;:::o;17532:215::-;17613:23;;;:48;;17671:27;;;-1:-1:-1;;;17671:27:204;-1:-1:-1;;;;17671:27:204;;;;-1:-1:-1;;;17671:27:204;;;17714:26;;;;;;17613:23;;17714:26;:::i;8986:428:206:-;9127:26;;;9085:7;9127:26;;;:8;:26;;;;;:38;9085:7;9193:43;9205:16;9127:38;9193:11;:43::i;:::-;9175:61;-1:-1:-1;9251:12:206;;;:70;;-1:-1:-1;9277:26:206;;;;;;;:8;:26;;;;;:44;;;9267:54;;9251:70;9247:136;;;9344:28;;-1:-1:-1;;;9344:28:206;;;;;5119:25:226;;;5092:18;;9344:28:206;4973:177:226;2078:126:152;1796:7;;;;2136:62;;2172:15;;-1:-1:-1;;;2172:15:152;;;;;;;;;;;2518:625:143;2613:7;2632:21;2656:32;2670:2;2674:7;2683:4;2656:13;:32::i;:::-;2632:56;-1:-1:-1;;;;;;2703:27:143;;2699:210;;2746:40;2778:7;3949:10;:17;;3922:24;;;;:15;:24;;;;;:44;;;3976:24;;;;;;;;;;;;3846:161;2746:40;2699:210;;;2824:2;-1:-1:-1;;;;;2807:19:143;:13;-1:-1:-1;;;;;2807:19:143;;2803:106;;2842:56;2875:13;2890:7;2842:32;:56::i;:::-;-1:-1:-1;;;;;2922:16:143;;2918:188;;2954:45;2991:7;2954:36;:45::i;:::-;2918:188;;;3037:2;-1:-1:-1;;;;;3020:19:143;:13;-1:-1:-1;;;;;3020:19:143;;3016:90;;3055:40;3083:2;3087:7;3055:27;:40::i;16734:171:204:-;16805:23;;;:48;;-1:-1:-1;;;;16805:48:204;-1:-1:-1;;;16805:48:204;;;16869:29;;;;;;16805:23;;16869:29;:::i;26354:415::-;26529:16;;26459:4;;;26555:185;26575:12;26571:1;:16;26555:185;;;26700:16;-1:-1:-1;;;;;26684:32:204;:9;26694:1;26684:12;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;;;26684:32:204;;26680:49;;26725:4;26718:11;;;;;;26680:49;26589:3;;26555:185;;;-1:-1:-1;26757:5:204;;26354:415;-1:-1:-1;;;;26354:415:204:o;3294:148:154:-;3352:13;3384:51;-1:-1:-1;;;;;3396:22:154;;448:2;3384:11;:51::i;1186:4022:148:-;1283:13;1515:4;:11;1530:1;1515:16;1511:31;;-1:-1:-1;1533:9:148;;;;;;;;;-1:-1:-1;1533:9:148;;;;1511:31;2480:20;2503:11;:69;;2571:1;2552:4;:11;2548:1;:15;;;;:::i;:::-;:19;;2566:1;2548:19;:::i;:::-;2547:25;;;;:::i;:::-;2503:69;;;2542:1;2523:4;:11;2537:1;2523:15;;;;:::i;:::-;2522:21;;;;:::i;:::-;2517:27;;:1;:27;:::i;:::-;2480:92;;2583:20;2617:12;-1:-1:-1;;;;;2606:24:148;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2606:24:148;;2583:47;;2778:1;2771:5;2767:13;2879:4;2871:6;2867:17;2912:4;2959;2953:11;2947:4;2943:22;3207:4;3199:6;3195:17;3249:8;3243:15;3288:4;3278:8;3271:22;3360:1259;3393:6;3384:7;3381:19;3360:1259;;;3495:1;3486:7;3482:15;3471:26;;3533:7;3527:14;4120:4;4112:5;4108:2;4104:14;4100:25;4090:8;4086:40;4080:47;4069:9;4061:67;4173:1;4162:9;4158:17;4145:30;;4263:4;4255:5;4251:2;4247:14;4243:25;4233:8;4229:40;4223:47;4212:9;4204:67;4316:1;4305:9;4301:17;4288:30;;4405:4;4397:5;4394:1;4390:13;4386:24;4376:8;4372:39;4366:46;4355:9;4347:66;4458:1;4447:9;4443:17;4430:30;;4539:4;4532:5;4528:16;4518:8;4514:31;4508:38;4497:9;4489:58;;4592:1;4581:9;4577:17;4564:30;;3360:1259;;;4680:28;;-1:-1:-1;;4722:446:148;;;;4907:1;4900:4;4894:11;4890:19;4931:1;4926:132;;;;5080:1;5075:79;;;;4883:271;;4926:132;4982:4;4978:1;4967:9;4963:17;4955:32;5035:4;5031:1;5020:9;5016:17;5008:32;4926:132;;5075:79;5131:4;5127:1;5116:9;5112:17;5104:32;4883:271;;4722:446;-1:-1:-1;5195:6:148;;1186:4022;-1:-1:-1;;;;;;1186:4022:148:o;11510:227:140:-;11561:21;11585:40;11601:1;11605:7;11622:1;11585:7;:40::i;:::-;11561:64;-1:-1:-1;;;;;;11639:27:140;;11635:96;;11689:31;;-1:-1:-1;;;11689:31:140;;;;;5119:25:226;;;5092:18;;11689:31:140;4973:177:226;21577:394:204;21696:16;;21666:4;;;21722:221;21742:3;21738:1;:7;21722:221;;;21786:16;-1:-1:-1;;;;;21770:32:204;:9;21780:1;21770:12;;;;;;;;:::i;:::-;;;;;;;;;;;-1:-1:-1;;;;;21770:12:204;:32;21766:167;;21837:9;21847:7;21853:1;21847:3;:7;:::i;:::-;21837:18;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;-1:-1:-1;;;;;21837:18:204;21822:9;21832:1;21822:12;;;;;;;;:::i;:::-;;;;;;;;;:33;;;;;-1:-1:-1;;;;;21822:33:204;;;;;-1:-1:-1;;;;;21822:33:204;;;;;;21873:9;:15;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;21873:15:204;;;;;-1:-1:-1;;;;;;21873:15:204;;;;;;-1:-1:-1;21873:15:204;;-1:-1:-1;21907:11:204;;-1:-1:-1;21907:11:204;21766:167;21747:3;;21722:221;;8861:795:140;8947:7;5824:16;;;:7;:16;;;;;;-1:-1:-1;;;;;5824:16:140;;;;9058:18;;;9054:86;;9092:37;9109:4;9115;9121:7;9092:16;:37::i;:::-;-1:-1:-1;;;;;9184:18:140;;;9180:256;;9300:48;9317:1;9321:7;9338:1;9342:5;9300:8;:48::i;:::-;-1:-1:-1;;;;;9391:15:140;;;;;;:9;:15;;;;;:20;;-1:-1:-1;;9391:20:140;;;9180:256;-1:-1:-1;;;;;9450:16:140;;;9446:107;;-1:-1:-1;;;;;9510:13:140;;;;;;:9;:13;;;;;:18;;9527:1;9510:18;;;9446:107;9563:16;;;;:7;:16;;;;;;:21;;-1:-1:-1;;;;;;9563:21:140;-1:-1:-1;;;;;9563:21:140;;;;;;;;;9600:27;;9563:16;;9600:27;;;;;;;9645:4;8861:795;-1:-1:-1;;;;8861:795:140:o;4624:1055:143:-;4886:22;4911:15;4921:4;4911:9;:15::i;:::-;4936:18;4957:26;;;:17;:26;;;;;;;;;-1:-1:-1;;;;;5058:18:143;;;;:12;:18;;;;;;4886:40;;-1:-1:-1;4957:26:143;5180:28;;;5176:325;;5224:19;5246:35;;;;;;;;;;;;5296:31;;;;;;:45;;;5413:30;;:17;:30;;;;;:43;;;5176:325;5594:26;;;;:17;:26;;;;;;;;5587:33;;;5637:35;;;;-1:-1:-1;5637:35:143;;5630:42;-1:-1:-1;4624:1055:143:o;5967:1061::-;6241:10;:17;6216:22;;6241:21;;6261:1;;6241:21;:::i;:::-;6272:18;6293:24;;;:15;:24;;;;;;6661:10;:26;;6216:46;;-1:-1:-1;6293:24:143;;6216:46;;6661:26;;;;;;:::i;:::-;;;;;;;;;6639:48;;6723:11;6698:10;6709;6698:22;;;;;;;;:::i;:::-;;;;;;;;;;;;:36;;;;6802:28;;;:15;:28;;;;;;;:41;;;6971:24;;;;;6964:31;7005:10;:16;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;6038:990;;;5967:1061;:::o;3437:214::-;3521:14;3554:1;3538:13;3548:2;3538:9;:13::i;:::-;:17;;;;:::i;:::-;-1:-1:-1;;;;;3565:16:143;;;;;;;:12;:16;;;;;;;;:24;;;;;;;;:34;;;3609:26;;;:17;:26;;;;;;:35;;;;-1:-1:-1;3437:214:143:o;2610:525:154:-;2685:13;2731:5;2710:18;2778:10;2782:6;2778:1;:10;:::i;:::-;:14;;2791:1;2778:14;:::i;:::-;-1:-1:-1;;;;;2768:25:154;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2768:25:154;;2746:47;;-1:-1:-1;;;2803:6:154;2810:1;2803:9;;;;;;;;:::i;:::-;;;;:15;-1:-1:-1;;;;;2803:15:154;;;;;;;;;-1:-1:-1;;;2828:6:154;2835:1;2828:9;;;;;;;;:::i;:::-;;;;:15;-1:-1:-1;;;;;2828:15:154;;;;;;;;-1:-1:-1;2858:9:154;2870:10;2874:6;2870:1;:10;:::i;:::-;:14;;2883:1;2870:14;:::i;:::-;2858:26;;2853:140;2890:1;2886;:5;2853:140;;;-1:-1:-1;;;2935:10:154;2948:3;2935:16;2924:28;;;;;;;:::i;:::-;;;;2912:6;2919:1;2912:9;;;;;;;;:::i;:::-;;;;:40;-1:-1:-1;;;;;2912:40:154;;;;;;;;-1:-1:-1;2981:1:154;2966:16;;;;;2893:3;;;:::i;:::-;;;2853:140;;;-1:-1:-1;3006:15:154;;3002:96;;3044:43;;-1:-1:-1;;;3044:43:154;;;;;29196:25:226;;;29237:18;;;29230:34;;;29169:18;;3044:43:154;29022:248:226;7105:368:140;7217:38;7231:5;7238:7;7247;7217:13;:38::i;:::-;7212:255;;-1:-1:-1;;;;;7275:19:140;;7271:186;;7321:31;;-1:-1:-1;;;7321:31:140;;;;;5119:25:226;;;5092:18;;7321:31:140;4973:177:226;7271:186:140;7398:44;;-1:-1:-1;;;7398:44:140;;-1:-1:-1;;;;;21179:32:226;;7398:44:140;;;21161:51:226;21228:18;;;21221:34;;;21134:18;;7398:44:140;20987:274:226;14794:662:140;14954:9;:31;;;-1:-1:-1;;;;;;14967:18:140;;;;14954:31;14950:460;;;15001:13;15017:22;15031:7;15017:13;:22::i;:::-;15001:38;-1:-1:-1;;;;;;15167:18:140;;;;;;:35;;;15198:4;-1:-1:-1;;;;;15189:13:140;:5;-1:-1:-1;;;;;15189:13:140;;;15167:35;:69;;;;;15207:29;15224:5;15231:4;15207:16;:29::i;:::-;15206:30;15167:69;15163:142;;;15263:27;;-1:-1:-1;;;15263:27:140;;-1:-1:-1;;;;;2816:32:226;;15263:27:140;;;2798:51:226;2771:18;;15263:27:140;2652:203:226;15163:142:140;15323:9;15319:81;;;15377:7;15373:2;-1:-1:-1;;;;;15357:28:140;15366:5;-1:-1:-1;;;;;15357:28:140;;;;;;;;;;;15319:81;14987:423;14950:460;-1:-1:-1;;15420:24:140;;;;:15;:24;;;;;:29;;-1:-1:-1;;;;;;15420:29:140;-1:-1:-1;;;;;15420:29:140;;;;;;;;;;14794:662::o;6401:272::-;6504:4;-1:-1:-1;;;;;6539:21:140;;;;;;:127;;;6586:7;-1:-1:-1;;;;;6577:16:140;:5;-1:-1:-1;;;;;6577:16:140;;:52;;;;6597:32;6614:5;6621:7;6597:16;:32::i;:::-;6577:88;;;-1:-1:-1;;6033:7:140;6059:24;;;:15;:24;;;;;;-1:-1:-1;;;;;6059:24:140;;;6633:32;;;;6520:146;-1:-1:-1;6401:272:140:o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;14:131:226;-1:-1:-1;;;;;;88:32:226;;78:43;;68:71;;135:1;132;125:12;150:245;208:6;261:2;249:9;240:7;236:23;232:32;229:52;;;277:1;274;267:12;229:52;316:9;303:23;335:30;359:5;335:30;:::i;688:381::-;765:8;775:6;829:3;822:4;814:6;810:17;806:27;796:55;;847:1;844;837:12;796:55;-1:-1:-1;870:20:226;;-1:-1:-1;;;;;902:30:226;;899:50;;;945:1;942;935:12;899:50;982:4;974:6;970:17;958:29;;1042:3;1035:4;1025:6;1022:1;1018:14;1010:6;1006:27;1002:38;999:47;996:67;;;1059:1;1056;1049:12;996:67;688:381;;;;;:::o;1074:477::-;1186:6;1194;1247:2;1235:9;1226:7;1222:23;1218:32;1215:52;;;1263:1;1260;1253:12;1215:52;1303:9;1290:23;-1:-1:-1;;;;;1328:6:226;1325:30;1322:50;;;1368:1;1365;1358:12;1322:50;1407:84;1483:7;1474:6;1463:9;1459:22;1407:84;:::i;:::-;1510:8;;1381:110;;-1:-1:-1;1074:477:226;-1:-1:-1;;;;1074:477:226:o;1556:250::-;1641:1;1651:113;1665:6;1662:1;1659:13;1651:113;;;1741:11;;;1735:18;1722:11;;;1715:39;1687:2;1680:10;1651:113;;;-1:-1:-1;;1798:1:226;1780:16;;1773:27;1556:250::o;1811:271::-;1853:3;1891:5;1885:12;1918:6;1913:3;1906:19;1934:76;2003:6;1996:4;1991:3;1987:14;1980:4;1973:5;1969:16;1934:76;:::i;:::-;2064:2;2043:15;-1:-1:-1;;2039:29:226;2030:39;;;;2071:4;2026:50;;1811:271;-1:-1:-1;;1811:271:226:o;2087:220::-;2236:2;2225:9;2218:21;2199:4;2256:45;2297:2;2286:9;2282:18;2274:6;2256:45;:::i;2312:226::-;2371:6;2424:2;2412:9;2403:7;2399:23;2395:32;2392:52;;;2440:1;2437;2430:12;2392:52;-1:-1:-1;2485:23:226;;2312:226;-1:-1:-1;2312:226:226:o;2860:173::-;2928:20;;-1:-1:-1;;;;;2977:31:226;;2967:42;;2957:70;;3023:1;3020;3013:12;3038:300;3106:6;3114;3167:2;3155:9;3146:7;3142:23;3138:32;3135:52;;;3183:1;3180;3173:12;3135:52;3206:29;3225:9;3206:29;:::i;:::-;3196:39;3304:2;3289:18;;;;3276:32;;-1:-1:-1;;;3038:300:226:o;3830:347::-;3881:8;3891:6;3945:3;3938:4;3930:6;3926:17;3922:27;3912:55;;3963:1;3960;3953:12;3912:55;-1:-1:-1;3986:20:226;;-1:-1:-1;;;;;4018:30:226;;4015:50;;;4061:1;4058;4051:12;4015:50;4098:4;4090:6;4086:17;4074:29;;4150:3;4143:4;4134:6;4126;4122:19;4118:30;4115:39;4112:59;;;4167:1;4164;4157:12;4182:786;4281:6;4289;4297;4305;4313;4366:2;4354:9;4345:7;4341:23;4337:32;4334:52;;;4382:1;4379;4372:12;4334:52;4422:9;4409:23;-1:-1:-1;;;;;4447:6:226;4444:30;4441:50;;;4487:1;4484;4477:12;4441:50;4526:58;4576:7;4567:6;4556:9;4552:22;4526:58;:::i;:::-;4603:8;;-1:-1:-1;4500:84:226;-1:-1:-1;4657:38:226;;-1:-1:-1;4691:2:226;4676:18;;4657:38;:::i;:::-;4647:48;;4748:2;4737:9;4733:18;4720:32;-1:-1:-1;;;;;4767:8:226;4764:32;4761:52;;;4809:1;4806;4799:12;4761:52;4848:60;4900:7;4889:8;4878:9;4874:24;4848:60;:::i;:::-;4182:786;;;;-1:-1:-1;4182:786:226;;-1:-1:-1;4927:8:226;;4822:86;4182:786;-1:-1:-1;;;4182:786:226:o;5155:186::-;5214:6;5267:2;5255:9;5246:7;5242:23;5238:32;5235:52;;;5283:1;5280;5273:12;5235:52;5306:29;5325:9;5306:29;:::i;5445:127::-;5506:10;5501:3;5497:20;5494:1;5487:31;5537:4;5534:1;5527:15;5561:4;5558:1;5551:15;5577:243;5664:1;5657:5;5654:12;5644:143;;5709:10;5704:3;5700:20;5697:1;5690:31;5744:4;5741:1;5734:15;5772:4;5769:1;5762:15;5644:143;5796:18;;5577:243::o;5905:954::-;5961:3;6005:5;5999:12;6032:6;6027:3;6020:19;6060:49;6101:6;6096:3;6092:16;6078:12;6060:49;:::i;:::-;6048:61;;6187:1;6183;6178:3;6174:11;6170:19;6162:4;6155:5;6151:16;6145:23;6141:49;6134:4;6129:3;6125:14;6118:73;6252:10;6244:4;6237:5;6233:16;6227:23;6223:40;6216:4;6211:3;6207:14;6200:64;6312:4;6305:5;6301:16;6295:23;6327:49;6370:4;6365:3;6361:14;6345;5422:10;5411:22;5399:35;;5346:94;6327:49;;6424:4;6417:5;6413:16;6407:23;6439:63;6496:4;6491:3;6487:14;6471;6439:63;:::i;:::-;;6550:4;6543:5;6539:16;6533:23;6565:47;6606:4;6601:3;6597:14;6581;470:13;463:21;451:34;;400:91;6565:47;;6660:4;6653:5;6649:16;6643:23;6675:47;6716:4;6711:3;6707:14;6691;470:13;463:21;451:34;;400:91;6675:47;;6770:4;6763:5;6759:16;6753:23;6785:48;6827:4;6822:3;6818:14;6802;5892:4;5881:16;5869:29;;5825:75;6864:278;7057:2;7046:9;7039:21;7020:4;7077:59;7132:2;7121:9;7117:18;7109:6;7077:59;:::i;7147:374::-;7224:6;7232;7240;7293:2;7281:9;7272:7;7268:23;7264:32;7261:52;;;7309:1;7306;7299:12;7261:52;7332:29;7351:9;7332:29;:::i;:::-;7322:39;;7380:38;7414:2;7403:9;7399:18;7380:38;:::i;:::-;7147:374;;7370:48;;-1:-1:-1;;;7487:2:226;7472:18;;;;7459:32;;7147:374::o;7526:409::-;7596:6;7604;7657:2;7645:9;7636:7;7632:23;7628:32;7625:52;;;7673:1;7670;7663:12;7625:52;7713:9;7700:23;-1:-1:-1;;;;;7738:6:226;7735:30;7732:50;;;7778:1;7775;7768:12;7732:50;7817:58;7867:7;7858:6;7847:9;7843:22;7817:58;:::i;7940:121::-;8025:10;8018:5;8014:22;8007:5;8004:33;7994:61;;8051:1;8048;8041:12;8066:245;8124:6;8177:2;8165:9;8156:7;8152:23;8148:32;8145:52;;;8193:1;8190;8183:12;8145:52;8232:9;8219:23;8251:30;8275:5;8251:30;:::i;8423:1147::-;8608:2;8590:21;;;8682:13;;8731:4;8711:18;;;8704:32;8785:19;;8649:3;8634:19;;8813:22;;;8571:4;;8893:21;;;;;8571:4;;8866:3;8851:19;;;8942:195;8956:6;8953:1;8950:13;8942:195;;;9021:13;;-1:-1:-1;;;;;9017:39:226;9005:52;;9086:2;9112:15;;;;9053:1;8971:9;;;;;9077:12;;;;8942:195;;;8946:3;9191:2;9183:6;9179:15;9173:22;9168:2;9157:9;9153:18;9146:50;9245:2;9237:6;9233:15;9227:22;9205:44;;9258:53;9307:2;9296:9;9292:18;9276:14;-1:-1:-1;;;;;8381:30:226;8369:43;;8316:102;9258:53;9360:2;9348:15;;9342:22;5422:10;5411:22;;9422:3;9407:19;;5399:35;9342:22;-1:-1:-1;9476:3:226;9464:16;;9458:23;5892:4;5881:16;;9538:4;9523:20;;5869:29;9458:23;-1:-1:-1;9561:3:226;8423:1147;-1:-1:-1;;;;;8423:1147:226:o;10205:610::-;10291:6;10299;10352:2;10340:9;10331:7;10327:23;10323:32;10320:52;;;10368:1;10365;10358:12;10320:52;10408:9;10395:23;-1:-1:-1;;;;;10433:6:226;10430:30;10427:50;;;10473:1;10470;10463:12;10427:50;10496:22;;10549:4;10541:13;;10537:27;-1:-1:-1;10527:55:226;;10578:1;10575;10568:12;10527:55;10618:2;10605:16;-1:-1:-1;;;;;10636:6:226;10633:30;10630:50;;;10676:1;10673;10666:12;10630:50;10729:7;10724:2;10714:6;10711:1;10707:14;10703:2;10699:23;10695:32;10692:45;10689:65;;;10750:1;10747;10740:12;10689:65;10781:2;10773:11;;;;;10803:6;;-1:-1:-1;10205:610:226;-1:-1:-1;;;10205:610:226:o;11276:1238::-;11682:3;11677;11673:13;11665:6;11661:26;11650:9;11643:45;11724:3;11719:2;11708:9;11704:18;11697:31;11624:4;11751:46;11792:3;11781:9;11777:19;11769:6;11751:46;:::i;:::-;11845:9;11837:6;11833:22;11828:2;11817:9;11813:18;11806:50;11879:33;11905:6;11897;11879:33;:::i;:::-;11943:2;11928:18;;11921:34;;;-1:-1:-1;;;;;11992:32:226;;11986:3;11971:19;;11964:61;12012:3;12041:19;;12034:35;;;12106:22;;;12100:3;12085:19;;12078:51;12178:13;;12200:22;;;12250:2;12276:15;;;;-1:-1:-1;12238:15:226;;;;-1:-1:-1;12319:169:226;12333:6;12330:1;12327:13;12319:169;;;12394:13;;12382:26;;12437:2;12463:15;;;;12428:12;;;;12355:1;12348:9;12319:169;;;-1:-1:-1;12505:3:226;;11276:1238;-1:-1:-1;;;;;;;;;;;11276:1238:226:o;12743:277::-;12823:6;12876:2;12864:9;12855:7;12851:23;12847:32;12844:52;;;12892:1;12889;12882:12;12844:52;12931:9;12918:23;12970:1;12963:5;12960:12;12950:40;;12986:1;12983;12976:12;13025:840;13231:4;13279:2;13268:9;13264:18;13309:2;13298:9;13291:21;13332:6;13367;13361:13;13398:6;13390;13383:22;13436:2;13425:9;13421:18;13414:25;;13498:2;13488:6;13485:1;13481:14;13470:9;13466:30;13462:39;13448:53;;13536:2;13528:6;13524:15;13557:1;13567:269;13581:6;13578:1;13575:13;13567:269;;;13674:2;13670:7;13658:9;13650:6;13646:22;13642:36;13637:3;13630:49;13702:54;13749:6;13740;13734:13;13702:54;:::i;:::-;13692:64;-1:-1:-1;13791:2:226;13814:12;;;;13779:15;;;;;13603:1;13596:9;13567:269;;;-1:-1:-1;13853:6:226;;13025:840;-1:-1:-1;;;;;;13025:840:226:o;13870:347::-;13935:6;13943;13996:2;13984:9;13975:7;13971:23;13967:32;13964:52;;;14012:1;14009;14002:12;13964:52;14035:29;14054:9;14035:29;:::i;:::-;14025:39;;14114:2;14103:9;14099:18;14086:32;14161:5;14154:13;14147:21;14140:5;14137:32;14127:60;;14183:1;14180;14173:12;14127:60;14206:5;14196:15;;;13870:347;;;;;:::o;14222:269::-;14279:6;14332:2;14320:9;14311:7;14307:23;14303:32;14300:52;;;14348:1;14345;14338:12;14300:52;14387:9;14374:23;14437:4;14430:5;14426:16;14419:5;14416:27;14406:55;;14457:1;14454;14447:12;14496:127;14557:10;14552:3;14548:20;14545:1;14538:31;14588:4;14585:1;14578:15;14612:4;14609:1;14602:15;14628:725;14670:5;14723:3;14716:4;14708:6;14704:17;14700:27;14690:55;;14741:1;14738;14731:12;14690:55;14781:6;14768:20;-1:-1:-1;;;;;14803:6:226;14800:30;14797:56;;;14833:18;;:::i;:::-;14882:2;14876:9;14974:2;14936:17;;-1:-1:-1;;14932:31:226;;;14965:2;14928:40;14924:54;14912:67;;-1:-1:-1;;;;;14994:34:226;;15030:22;;;14991:62;14988:88;;;15056:18;;:::i;:::-;15092:2;15085:22;15116;;;15157:19;;;15178:4;15153:30;15150:39;-1:-1:-1;15147:59:226;;;15202:1;15199;15192:12;15147:59;15266:6;15259:4;15251:6;15247:17;15240:4;15232:6;15228:17;15215:58;15321:1;15293:19;;;15314:4;15289:30;15282:41;;;;15297:6;14628:725;-1:-1:-1;;;14628:725:226:o;15358:583::-;15453:6;15461;15469;15477;15530:3;15518:9;15509:7;15505:23;15501:33;15498:53;;;15547:1;15544;15537:12;15498:53;15570:29;15589:9;15570:29;:::i;:::-;15560:39;;15618:38;15652:2;15641:9;15637:18;15618:38;:::i;:::-;15608:48;-1:-1:-1;15725:2:226;15710:18;;15697:32;;-1:-1:-1;15804:2:226;15789:18;;15776:32;-1:-1:-1;;;;;15820:30:226;;15817:50;;;15863:1;15860;15853:12;15817:50;15886:49;15927:7;15918:6;15907:9;15903:22;15886:49;:::i;:::-;15876:59;;;15358:583;;;;;;;:::o;16143:236::-;16234:6;16294:3;16282:9;16273:7;16269:23;16265:33;16310:2;16307:22;;;16325:1;16322;16315:12;16307:22;-1:-1:-1;16364:9:226;;16143:236;-1:-1:-1;;16143:236:226:o;16384:468::-;16470:6;16478;16486;16539:2;16527:9;16518:7;16514:23;16510:32;16507:52;;;16555:1;16552;16545:12;16507:52;16595:9;16582:23;-1:-1:-1;;;;;16620:6:226;16617:30;16614:50;;;16660:1;16657;16650:12;16614:50;16683:49;16724:7;16715:6;16704:9;16700:22;16683:49;:::i;:::-;16673:59;;;16751:38;16785:2;16774:9;16770:18;16751:38;:::i;:::-;16741:48;;16808:38;16842:2;16831:9;16827:18;16808:38;:::i;:::-;16798:48;;16384:468;;;;;:::o;17039:260::-;17107:6;17115;17168:2;17156:9;17147:7;17143:23;17139:32;17136:52;;;17184:1;17181;17174:12;17136:52;17207:29;17226:9;17207:29;:::i;:::-;17197:39;;17255:38;17289:2;17278:9;17274:18;17255:38;:::i;:::-;17245:48;;17039:260;;;;;:::o;17304:848::-;17646:3;17635:9;17628:22;17609:4;17667:46;17708:3;17697:9;17693:19;17685:6;17667:46;:::i;:::-;-1:-1:-1;;;;;17749:32:226;;17744:2;17729:18;;17722:60;17830:10;17818:23;;;17813:2;17798:18;;17791:51;17878:23;;17873:2;17858:18;;17851:51;17659:54;-1:-1:-1;17911:60:226;17966:3;17951:19;;17943:6;17911:60;:::i;:::-;18015:14;;18008:22;18002:3;17987:19;;17980:51;18075:14;;18068:22;18062:3;18047:19;;18040:51;18140:4;18128:17;18122:3;18107:19;;;18100:46;17304:848;;-1:-1:-1;;;;;17304:848:226:o;18157:127::-;18218:10;18213:3;18209:20;18206:1;18199:31;18249:4;18246:1;18239:15;18273:4;18270:1;18263:15;18289:127;18350:10;18345:3;18341:20;18338:1;18331:31;18381:4;18378:1;18371:15;18405:4;18402:1;18395:15;18421:128;18488:9;;;18509:11;;;18506:37;;;18523:18;;:::i;18554:389::-;18738:2;18723:18;;-1:-1:-1;;;;;18772:26:226;18791:6;18772:26;:::i;:::-;18768:52;18750:71;;18887:4;18875:17;;;18862:31;18909:20;;;;18902:35;;;;18554:389;:::o;18948:380::-;19027:1;19023:12;;;;19070;;;19091:61;;19145:4;19137:6;19133:17;19123:27;;19091:61;19198:2;19190:6;19187:14;19167:18;19164:38;19161:161;;19244:10;19239:3;19235:20;19232:1;19225:31;19279:4;19276:1;19269:15;19307:4;19304:1;19297:15;19161:161;;18948:380;;;:::o;19333:168::-;19406:9;;;19437;;19454:15;;;19448:22;;19434:37;19424:71;;19475:18;;:::i;19506:125::-;19571:9;;;19592:10;;;19589:36;;;19605:18;;:::i;19636:127::-;19697:10;19692:3;19688:20;19685:1;19678:31;19728:4;19725:1;19718:15;19752:4;19749:1;19742:15;19768:120;19808:1;19834;19824:35;;19839:18;;:::i;:::-;-1:-1:-1;19873:9:226;;19768:120::o;19893:271::-;20076:6;20068;20063:3;20050:33;20032:3;20102:16;;20127:13;;;20102:16;19893:271;-1:-1:-1;19893:271:226:o;20169:617::-;20450:25;;;20506:2;20491:18;;20484:34;;;;-1:-1:-1;;;;;20554:32:226;;;20549:2;20534:18;;20527:60;20623:32;;20618:2;20603:18;;20596:60;20705:4;20693:17;20687:3;20672:19;;20665:46;-1:-1:-1;;;;;20748:31:226;20574:3;20727:19;;20720:60;20437:3;20422:19;;20169:617::o;20791:191::-;-1:-1:-1;;;;;20859:26:226;;;20887;;;20855:59;;20926:27;;20923:53;;;20956:18;;:::i;21825:167::-;21920:10;21893:18;;;21913;;;21889:43;;21944:19;;21941:45;;;21966:18;;:::i;21997:170::-;22094:10;22087:18;;;22067;;;22063:43;;22118:20;;22115:46;;;22141:18;;:::i;22172:221::-;22325:2;22310:18;;22337:50;22314:9;22369:6;22337:50;:::i;22398:175::-;22435:3;22479:4;22472:5;22468:16;22508:4;22499:7;22496:17;22493:43;;22516:18;;:::i;:::-;22565:1;22552:15;;22398:175;-1:-1:-1;;22398:175:226:o;22578:690::-;22761:19;;22789:21;;22879:2;22868:14;;22855:28;22909:1;22899:12;;22892:29;22990:2;22979:14;;22966:28;23020:1;23010:12;;23003:29;23069:1;23059:12;;23119:2;23108:14;;23095:28;23132:32;23095:28;23132:32;:::i;:::-;23199:17;;-1:-1:-1;;23195:39:226;23249:10;23236:24;;;;23192:69;23173:89;;-1:-1:-1;;22578:690:226:o;23273:1317::-;23774:66;23769:3;23762:79;-1:-1:-1;;;23866:2:226;23861:3;23857:12;23850:30;23744:3;23909:6;23903:13;23925:73;23991:6;23986:2;23981:3;23977:12;23972:2;23964:6;23960:15;23925:73;:::i;:::-;24062:66;24057:2;24017:16;;;24049:11;;;24042:87;24158:34;24153:2;24145:11;;24138:55;24223:34;24217:3;24209:12;;24202:56;-1:-1:-1;;;24282:3:226;24274:12;;24267:77;24369:13;;24391:75;24369:13;24451:3;24443:12;;24438:2;24426:15;;24391:75;:::i;:::-;-1:-1:-1;;;24527:12:226;24489:17;;;;24527:12;;;24520:36;24572:12;;;23273:1317;-1:-1:-1;;;;23273:1317:226:o;24595:451::-;24847:31;24842:3;24835:44;24817:3;24908:6;24902:13;24924:75;24992:6;24987:2;24982:3;24978:12;24971:4;24963:6;24959:17;24924:75;:::i;:::-;25019:16;;;;25037:2;25015:25;;24595:451;-1:-1:-1;;24595:451:226:o;25176:517::-;25277:2;25272:3;25269:11;25266:421;;;25313:5;25310:1;25303:16;25357:4;25354:1;25344:18;25427:2;25415:10;25411:19;25408:1;25404:27;25398:4;25394:38;25463:4;25451:10;25448:20;25445:47;;;-1:-1:-1;25486:4:226;25445:47;25541:2;25536:3;25532:12;25529:1;25525:20;25519:4;25515:31;25505:41;;25596:81;25614:2;25607:5;25604:13;25596:81;;;25673:1;25659:16;;25640:1;25629:13;25596:81;;25869:1295;25993:3;25987:10;-1:-1:-1;;;;;26012:6:226;26009:30;26006:56;;;26042:18;;:::i;:::-;26071:96;26160:6;26120:38;26152:4;26146:11;26120:38;:::i;:::-;26114:4;26071:96;:::i;:::-;26216:4;26247:2;26236:14;;26264:1;26259:648;;;;26951:1;26968:6;26965:89;;;-1:-1:-1;27020:19:226;;;27014:26;26965:89;-1:-1:-1;;25826:1:226;25822:11;;;25818:24;25814:29;25804:40;25850:1;25846:11;;;25801:57;27067:81;;26229:929;;26259:648;25123:1;25116:14;;;25160:4;25147:18;;-1:-1:-1;;26295:20:226;;;26412:222;26426:7;26423:1;26420:14;26412:222;;;26508:19;;;26502:26;26487:42;;26615:4;26600:20;;;;26568:1;26556:14;;;;26442:12;26412:222;;;26416:3;26662:6;26653:7;26650:19;26647:201;;;26723:19;;;26717:26;-1:-1:-1;;26806:1:226;26802:14;;;26818:3;26798:24;26794:37;26790:42;26775:58;26760:74;;26647:201;-1:-1:-1;;;;26894:1:226;26878:14;;;26874:22;26861:36;;-1:-1:-1;25869:1295:226:o;27169:738::-;27218:3;27259:5;27253:12;27288:36;27314:9;27288:36;:::i;:::-;27333:19;;;27383:1;27368:17;;27394:150;;;;27558:1;27553:348;;;;27361:540;;27394:150;27457:3;27453:8;27442:9;27438:24;27431:4;27426:3;27422:14;27415:48;27529:4;27517:6;27510:14;27503:22;27500:1;27496:30;27491:3;27487:40;27483:51;27476:58;;27394:150;;27553:348;27584:5;27581:1;27574:16;27631:4;27628:1;27618:18;27658:1;27672:177;27686:6;27683:1;27680:13;27672:177;;;27783:7;27777:14;27770:4;27766:1;27761:3;27757:11;27753:22;27746:46;27833:1;27824:7;27820:15;27809:26;;27708:4;27705:1;27701:12;27696:17;;27672:177;;;27873:11;;27886:4;27869:22;;-1:-1:-1;;27361:540:226;;;;27169:738;;;;:::o;27912:965::-;28106:2;28095:9;28088:21;28145:6;28140:2;28129:9;28125:18;28118:34;28069:4;28175:53;28223:3;28212:9;28208:19;28200:6;28175:53;:::i;:::-;28272:4;28260:17;;28254:24;-1:-1:-1;;;;;28306:35:226;;28358:2;28343:18;;2597:44;28397:3;28393:19;;;28414:10;28389:36;28442:2;28427:18;;5399:35;28481:3;28477:19;;;28498:10;28473:36;28526:3;28511:19;;5399:35;28540:84;28619:3;28608:9;28604:19;28597:4;28585:9;28580:3;28576:19;28572:30;28540:84;:::i;:::-;28633:68;28696:3;28685:9;28681:19;28674:4;28662:9;28657:3;28653:19;28649:30;470:13;463:21;451:34;;400:91;28633:68;28710;28773:3;28762:9;28758:19;28751:4;28739:9;28734:3;28730:19;28726:30;470:13;463:21;451:34;;400:91;28710:68;28808:3;28804:19;;;28840:6;28825:22;;5869:29;28787:61;5825:75;28882:135;28921:3;28942:17;;;28939:43;;28962:18;;:::i;:::-;-1:-1:-1;29009:1:226;28998:13;;28882:135::o;29275:148::-;29363:4;29342:12;;;29356;;;29338:31;;29381:13;;29378:39;;;29397:18;;:::i;29428:157::-;29458:1;29492:4;29489:1;29485:12;29516:3;29506:37;;29523:18;;:::i;:::-;29575:3;29568:4;29565:1;29561:12;29557:22;29552:27;;;29428:157;;;;:::o;29590:188::-;29628:3;29672:10;29665:5;29661:22;29707:10;29698:7;29695:23;29692:49;;29721:18;;:::i;29783:485::-;-1:-1:-1;;;;;30014:32:226;;;29996:51;;30083:32;;30078:2;30063:18;;30056:60;30147:2;30132:18;;30125:34;;;30195:3;30190:2;30175:18;;30168:31;;;-1:-1:-1;;30216:46:226;;30242:19;;30234:6;30216:46;:::i;:::-;30208:54;29783:485;-1:-1:-1;;;;;;29783:485:226:o;30273:249::-;30342:6;30395:2;30383:9;30374:7;30370:23;30366:32;30363:52;;;30411:1;30408;30401:12;30363:52;30443:9;30437:16;30462:30;30486:5;30462:30;:::i;30527:136::-;30566:3;30594:5;30584:39;;30603:18;;:::i;:::-;-1:-1:-1;;;30639:18:226;;30527:136::o;30668:448::-;30920:28;30915:3;30908:41;30890:3;30978:6;30972:13;30994:75;31062:6;31057:2;31052:3;31048:12;31041:4;31033:6;31029:17;30994:75;:::i;:::-;31089:16;;;;31107:2;31085:25;;30668:448;-1:-1:-1;;30668:448:226:o;31121:127::-;31182:10;31177:3;31173:20;31170:1;31163:31;31213:4;31210:1;31203:15;31237:4;31234:1;31227:15", "linkReferences": {}, "immutableReferences": {"71538": [{"start": 9844, "length": 32}], "71540": [{"start": 9879, "length": 32}], "71542": [{"start": 9959, "length": 32}], "71544": [{"start": 9997, "length": 32}], "71546": [{"start": 9811, "length": 32}]}}, "methodIdentifiers": {"SYSTEM_ADDRESS()": "3434735f", "activate()": "0f15f4c0", "allocateIssuance()": "355811a7", "applyIncentives((address,uint256)[])": "0a36cdef", "applySlashes((address,uint256)[])": "02ae337b", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "beginExit()": "2228dfa0", "burn(address)": "89afcb44", "claimStakeRewards(address)": "2b9f4985", "concludeEpoch(address[])": "7b55a300", "delegateStake(bytes,address,bytes)": "0cc1026b", "delegationDigest(bytes,address,address)": "e3716d04", "eip712Domain()": "84b0196e", "epochInfo(uint256)": "3894228e", "futureEpochInfo(uint256)": "59ca93b6", "getApproved(uint256)": "081812fc", "getBalance(address)": "f8b2cb4f", "getCommitteeValidators(uint32)": "a0201be5", "getCurrentEpoch()": "b97dd9e2", "getCurrentEpochInfo()": "babc394f", "getCurrentStakeConfig()": "7d06fdf8", "getCurrentStakeVersion()": "67398331", "getEpochInfo(uint32)": "374ed18c", "getRewards(address)": "79ee54f7", "getValidator(address)": "1904bb2e", "getValidators(uint8)": "8cc05eda", "isApprovedForAll(address,address)": "e985e9c5", "isRetired(address)": "6d3c6275", "issuance()": "863623bb", "mint(address)": "6a627842", "name()": "06fdde03", "owner()": "8da5cb5b", "ownerOf(uint256)": "6352211e", "pause()": "8456cb59", "paused()": "5c975abb", "renounceOwnership()": "715018a6", "safeTransferFrom(address,address,uint256)": "42842e0e", "safeTransferFrom(address,address,uint256,bytes)": "b88d4fde", "setApprovalForAll(address,bool)": "a22cb465", "stake(bytes)": "2d1e0c02", "stakeConfig(uint8)": "a71954ec", "supportsInterface(bytes4)": "01ffc9a7", "symbol()": "95d89b41", "tokenByIndex(uint256)": "4f6ccce7", "tokenOfOwnerByIndex(address,uint256)": "2f745c59", "tokenURI(uint256)": "c87b56dd", "totalSupply()": "18160ddd", "transferFrom(address,address,uint256)": "23b872dd", "transferOwnership(address)": "f2fde38b", "unpause()": "3f4ba83a", "unstake(address)": "f2888dbb", "upgradeStakeVersion((uint256,uint256,uint256,uint32))": "c05df7ac", "validators(address)": "fa52c7d8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"stakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minWithdrawAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"}],\"internalType\":\"struct IStakeManager.StakeConfig\",\"name\":\"genesisConfig_\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"internalType\":\"struct IConsensusRegistry.ValidatorInfo[]\",\"name\":\"initialValidators_\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"owner_\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"AlreadyDefined\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"CommitteeRequirement\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DuplicateBLSPubkey\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC721EnumerableForbiddenBatchMint\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC721IncorrectOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ERC721InsufficientApproval\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC721InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"ERC721InvalidOperator\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC721InvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC721InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC721InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ERC721NonexistentToken\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"ERC721OutOfBoundsIndex\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"GenesisArityMismatch\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"withdrawAmount\",\"type\":\"uint256\"}],\"name\":\"InsufficientRewards\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidBLSPubkey\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"minCommitteeSize\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"providedCommitteeSize\",\"type\":\"uint256\"}],\"name\":\"InvalidCommitteeSize\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"duration\",\"type\":\"uint32\"}],\"name\":\"InvalidDuration\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"InvalidEpoch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidProof\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"stakeAmount\",\"type\":\"uint256\"}],\"name\":\"InvalidStakeAmount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"status\",\"type\":\"uint8\"}],\"name\":\"InvalidStatus\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSupply\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"InvalidTokenId\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidValidatorAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LowLevelCallFailure\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"NotRecipient\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotTransferable\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"NotValidator\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"invalidCaller\",\"type\":\"address\"}],\"name\":\"OnlySystemCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Reentrancy\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RequiresConsensusNFT\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"StringsInsufficientHexLength\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"address[]\",\"name\":\"committee\",\"type\":\"address[]\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"blockHeight\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.EpochInfo\",\"name\":\"epoch\",\"type\":\"tuple\"}],\"name\":\"NewEpoch\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"claimant\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"rewards\",\"type\":\"uint256\"}],\"name\":\"RewardsClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"validator\",\"type\":\"tuple\"}],\"name\":\"ValidatorActivated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"validator\",\"type\":\"tuple\"}],\"name\":\"ValidatorExited\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"validator\",\"type\":\"tuple\"}],\"name\":\"ValidatorPendingActivation\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"validator\",\"type\":\"tuple\"}],\"name\":\"ValidatorPendingExit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"validator\",\"type\":\"tuple\"}],\"name\":\"ValidatorRetired\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"indexed\":false,\"internalType\":\"struct Slash\",\"name\":\"slash\",\"type\":\"tuple\"}],\"name\":\"ValidatorSlashed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"indexed\":false,\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"validator\",\"type\":\"tuple\"}],\"name\":\"ValidatorStaked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"SYSTEM_ADDRESS\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"activate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allocateIssuance\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"consensusHeaderCount\",\"type\":\"uint256\"}],\"internalType\":\"struct RewardInfo[]\",\"name\":\"rewardInfos\",\"type\":\"tuple[]\"}],\"name\":\"applyIncentives\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"internalType\":\"struct Slash[]\",\"name\":\"slashes\",\"type\":\"tuple[]\"}],\"name\":\"applySlashes\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"beginExit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"claimStakeRewards\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"futureCommittee\",\"type\":\"address[]\"}],\"name\":\"concludeEpoch\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"validatorSig\",\"type\":\"bytes\"}],\"name\":\"delegateStake\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegator\",\"type\":\"address\"}],\"name\":\"delegationDigest\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"epochInfo\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"blockHeight\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"futureEpochInfo\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"blockHeight\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"getBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"getCommitteeValidators\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"internalType\":\"struct IConsensusRegistry.ValidatorInfo[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentEpoch\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentEpochInfo\",\"outputs\":[{\"components\":[{\"internalType\":\"address[]\",\"name\":\"committee\",\"type\":\"address[]\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"blockHeight\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"internalType\":\"struct IConsensusRegistry.EpochInfo\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentStakeConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"stakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minWithdrawAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"}],\"internalType\":\"struct IStakeManager.StakeConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentStakeVersion\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"getEpochInfo\",\"outputs\":[{\"components\":[{\"internalType\":\"address[]\",\"name\":\"committee\",\"type\":\"address[]\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"blockHeight\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"internalType\":\"struct IConsensusRegistry.EpochInfo\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"getRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"getValidator\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"internalType\":\"struct IConsensusRegistry.ValidatorInfo\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"status\",\"type\":\"uint8\"}],\"name\":\"getValidators\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"internalType\":\"struct IConsensusRegistry.ValidatorInfo[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"isRetired\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"issuance\",\"outputs\":[{\"internalType\":\"address payable\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"}],\"name\":\"stake\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"version\",\"type\":\"uint8\"}],\"name\":\"stakeConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"stakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minWithdrawAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"}],\"internalType\":\"struct IStakeManager.StakeConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"tokenByIndex\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"tokenOfOwnerByIndex\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"}],\"name\":\"unstake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"stakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minWithdrawAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"epochIssuance\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"epochDuration\",\"type\":\"uint32\"}],\"internalType\":\"struct IStakeManager.StakeConfig\",\"name\":\"newConfig\",\"type\":\"tuple\"}],\"name\":\"upgradeStakeVersion\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"validators\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"blsPubkey\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"validatorAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"activationEpoch\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"exitEpoch\",\"type\":\"uint32\"},{\"internalType\":\"enum IConsensusRegistry.ValidatorStatus\",\"name\":\"currentStatus\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"isRetired\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isDelegated\",\"type\":\"bool\"},{\"internalType\":\"uint8\",\"name\":\"stakeVersion\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Telcoin Association\",\"details\":\"This contract should be deployed to a predefined system address for use with system calls\",\"errors\":{\"ERC721EnumerableForbiddenBatchMint()\":[{\"details\":\"Batch mint is not allowed.\"}],\"ERC721IncorrectOwner(address,uint256,address)\":[{\"details\":\"Indicates an error related to the ownership over a particular token. Used in transfers.\",\"params\":{\"owner\":\"Address of the current owner of a token.\",\"sender\":\"Address whose tokens are being transferred.\",\"tokenId\":\"Identifier number of a token.\"}}],\"ERC721InsufficientApproval(address,uint256)\":[{\"details\":\"Indicates a failure with the `operator`\\u2019s approval. Used in transfers.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\",\"tokenId\":\"Identifier number of a token.\"}}],\"ERC721InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC721InvalidOperator(address)\":[{\"details\":\"Indicates a failure with the `operator` to be approved. Used in approvals.\",\"params\":{\"operator\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC721InvalidOwner(address)\":[{\"details\":\"Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20. Used in balance queries.\",\"params\":{\"owner\":\"Address of the current owner of a token.\"}}],\"ERC721InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC721InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC721NonexistentToken(uint256)\":[{\"details\":\"Indicates a `tokenId` whose `owner` is the zero address.\",\"params\":{\"tokenId\":\"Identifier number of a token.\"}}],\"ERC721OutOfBoundsIndex(address,uint256)\":[{\"details\":\"An `owner`'s token query was out of bounds for `index`. NOTE: The owner being `address(0)` indicates a global out of bounds index.\"}],\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"Reentrancy()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"StringsInsufficientHexLength(uint256,uint256)\":[{\"details\":\"The `value` string doesn't fit in the specified `length`.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when `owner` enables `approved` to manage the `tokenId` token.\"},\"ApprovalForAll(address,address,bool)\":{\"details\":\"Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.\"},\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `tokenId` token is transferred from `from` to `to`.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"activate()\":{\"details\":\"Self-activation function for validators, gaining `PendingActivation` status and setting next epoch as activation epoch to ensure rewards eligibility only after completing a full epoch\"},\"allocateIssuance()\":{\"details\":\"Permissioned function to allocate TEL for epoch issuance, ie consensus block rewards\"},\"applyIncentives((address,uint256)[])\":{\"details\":\"The network's epoch issuance distribution method, rewarding stake originators based on initial stake and on the validator's performance (consensus header count)\"},\"applySlashes((address,uint256)[])\":{\"details\":\"The network's slashing mechanism, which penalizes validators for misbehaving\"},\"balanceOf(address)\":{\"details\":\"See {IERC721-balanceOf}.\"},\"beginExit()\":{\"details\":\"Issues an exit request for a validator to be retired from the `Active` validator set\"},\"burn(address)\":{\"details\":\"In the case of malicious or erroneous node operator behavior, governance can use this function to burn a validator's `ConsensusNFT` and immediately eject from consensus committees if applicableIntended for sparing use; only reverts if burning results in empty committee\",\"params\":{\"from\":\"Refers to the struct member `ValidatorInfo.validatorAddress` in `IConsensusRegistry`\"}},\"claimStakeRewards(address)\":{\"details\":\"Used by rewardees to claim staking rewards\"},\"concludeEpoch(address[])\":{\"details\":\"Accepts the committee of voting validators for 2 epochs in the future\",\"params\":{\"newCommittee\":\"The future validator committee for `$.currentEpoch + 3`\"}},\"constructor\":{\"details\":\"Stake for `initialValidators_` is allocated directly to the ConsensusRegistry balance and decremented directly from InterchainTEL within the protocol on the rust sideOnly governance delegation is enabled at genesis\",\"params\":{\"initialValidators_\":\"The initial validator set running Telcoin Network; these validators will comprise the voter committee for the first three epochs, ie `epochInfo[0:2]`\"}},\"delegateStake(bytes,address,bytes)\":{\"details\":\"Accepts delegated stake from a non-validator caller authorized by a validator's EIP712 signature\"},\"delegationDigest(bytes,address,address)\":{\"returns\":{\"_0\":\"_ EIP-712 typed struct hash used to enable delegated proof of stake\"}},\"eip712Domain()\":{\"details\":\"See: https://eips.ethereum.org/EIPS/eip-5267\"},\"getApproved(uint256)\":{\"details\":\"See {IERC721-getApproved}.\"},\"getBalance(address)\":{\"details\":\"Returns staking information for the given address\"},\"getCommitteeValidators(uint32)\":{\"details\":\"Fetches the committee for a given epoch\"},\"getCurrentEpoch()\":{\"details\":\"Returns the current epoch\"},\"getCurrentEpochInfo()\":{\"details\":\"Returns the current epoch's committee and block height\"},\"getCurrentStakeConfig()\":{\"details\":\"Returns the current stake configuration\"},\"getCurrentStakeVersion()\":{\"details\":\"Returns the current version\"},\"getEpochInfo(uint32)\":{\"details\":\"Returns information about the provided epoch. Only four latest & two future epochs are stored\"},\"getRewards(address)\":{\"details\":\"Fetches the claimable rewards accrued for a given validator address\",\"returns\":{\"_0\":\"_ The validator's claimable rewards, not including the validator's stake\"}},\"getValidator(address)\":{\"details\":\"Fetches the `ValidatorInfo` for a given `validatorAddress == ConsensusNFT tokenId`\"},\"getValidators(uint8)\":{\"details\":\"Returns an array of unretired validators matching the provided status\",\"params\":{\"\":\"`Active` queries also include validators pending activation or exit since all three remain eligible for committee service in the next epoch\"}},\"isApprovedForAll(address,address)\":{\"details\":\"See {IERC721-isApprovedForAll}.\"},\"isRetired(address)\":{\"details\":\"Returns whether a validator is exited && unstaked, ie \\\"retired\\\"\"},\"mint(address)\":{\"details\":\"The StakeManager's ERC721 ledger serves a permissioning role over validators, requiring Telcoin governance to approve each node operator and manually issue them a `ConsensusNFT`\",\"params\":{\"to\":\"Refers to the struct member `ValidatorInfo.validatorAddress` in `IConsensusRegistry`\"}},\"name()\":{\"details\":\"See {IERC721Metadata-name}.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"ownerOf(uint256)\":{\"details\":\"See {IERC721-ownerOf}.\"},\"pause()\":{\"details\":\"Emergency function to pause validator and stake management\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"safeTransferFrom(address,address,uint256)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"safeTransferFrom(address,address,uint256,bytes)\":{\"details\":\"See {IERC721-safeTransferFrom}.\"},\"stake(bytes)\":{\"details\":\"Accepts the native TEL stake amount from the calling validator, enabling later self-activation\"},\"stakeConfig(uint8)\":{\"details\":\"Returns the queried stake configuration\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"symbol()\":{\"details\":\"See {IERC721Metadata-symbol}.\"},\"tokenByIndex(uint256)\":{\"details\":\"See {IERC721Enumerable-tokenByIndex}.\"},\"tokenOfOwnerByIndex(address,uint256)\":{\"details\":\"See {IERC721Enumerable-tokenOfOwnerByIndex}.\"},\"totalSupply()\":{\"details\":\"See {IERC721Enumerable-totalSupply}.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"unpause()\":{\"details\":\"Emergency function to unpause validator and stake management\"},\"unstake(address)\":{\"details\":\"Returns previously staked funds in addition to accrued rewards, if any, to the staker\"},\"upgradeStakeVersion((uint256,uint256,uint256,uint32))\":{\"details\":\"Permissioned function to upgrade stake, withdrawal, and consensus block reward configurations\"}},\"stateVariables\":{\"PENDING_EPOCH\":{\"details\":\"Signals a validator's pending status until activation/exit to correctly apply incentives\"}},\"title\":\"ConsensusRegistry\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"activate()\":{\"notice\":\"Caller must own a ConsensusNFT and be `Staked` status, ie staked or delegated\"},\"allocateIssuance()\":{\"notice\":\"Allocated TEL cannot be recovered; it is effectively burned cryptographically The only way received TEL can be re-minted is as staking issuance rewardsOnly governance may burn TEL in this manner\"},\"applyIncentives((address,uint256)[])\":{\"notice\":\"Stake originators are either a delegator if one exists, or the validator itselfCalled just before concluding the current epochNot yet enabled during pilot, but scaffolding is included here. For the time being, system calls to this fn can provide empty calldata arrays\"},\"applySlashes((address,uint256)[])\":{\"notice\":\"Called just before concluding the current epochNot yet enabled during pilot, but scaffolding is included here. For the time being, system calls to this fn can provide empty calldata arrays\"},\"approve(address,uint256)\":{\"notice\":\"Wouldn't do anything because transfers are disabled but explicitly disallow anyway\"},\"beginExit()\":{\"notice\":\"Reverts if the exit queue is full, ie if active validator count would drop too low\"},\"concludeEpoch(address[])\":{\"notice\":\"Voting Validator Committee changes at the end every epoch via syscall\"},\"delegateStake(bytes,address,bytes)\":{\"notice\":\"`validatorAddress` must be a validator already in possession of a `ConsensusNFT`\"},\"delegationDigest(bytes,address,address)\":{\"notice\":\"Returns the delegation digest that a validator should sign to accept a delegation\"},\"getEpochInfo(uint32)\":{\"notice\":\"When querying for future epochs, `blockHeight` will be 0 as they are not yet known\"},\"isRetired(address)\":{\"notice\":\"After retiring, a validator's `tokenId == validatorAddress` cannot be reused\"},\"mint(address)\":{\"notice\":\"For each mintee, `tokenId == uint160(to)`Access-gated in ConsensusRegistry to its owner, which is a Telcoin governance address\"},\"pause()\":{\"notice\":\"Does not pause system callable or ConsensusNFT fns. Only accessible by `owner`\"},\"setApprovalForAll(address,bool)\":{\"notice\":\"Wouldn't do anything because transfers are disabled but explicitly disallow anyway\"},\"stake(bytes)\":{\"notice\":\"Caller must already have been issued a `ConsensusNFT` by Telcoin governance\"},\"tokenURI(uint256)\":{\"notice\":\"Read-only mechanism, not yet live\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"Consensus NFTs are soulbound to validators and cannot be transferred unless burned\"},\"unpause()\":{\"notice\":\"Does not affect system callable or ConsensusNFT fns. Only accessible by `owner`\"},\"unstake(address)\":{\"notice\":\"May be used to reverse validator onboarding pre-activation or permanently retire after full exitOnce unstaked and retired, validator addresses cannot be reused\"},\"upgradeStakeVersion((uint256,uint256,uint256,uint32))\":{\"notice\":\"The new version takes effect in the next epoch\"}},\"notice\":\"A Telcoin ContractThis contract manages consensus validator external keys, staking, and committees\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/consensus/ConsensusRegistry.sol\":\"ConsensusRegistry\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/\",\":@axelar-network/=node_modules/@axelar-network/\",\":@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/\",\":@openzeppelin/=node_modules/@openzeppelin/\",\":@uniswap/=node_modules/@uniswap/\",\":ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/\",\":forge-std/=node_modules/forge-std/src/\",\":recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/\",\":solady/=node_modules/solady/src/\",\":telcoin-contracts/=node_modules/telcoin-contracts/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b\",\"dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol\":{\"keccak256\":\"0x39ed367e54765186281efcfe83e47cf0ad62cc879f10e191360712507125f29a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2c5ae6d85bd48cca8d6d2fcec8c63efd86f56f8a5832577a47e403ce0e65cb09\",\"dweb:/ipfs/QmUtcS8AbRSWhuc61puYet58os8FvSqm329ChoW8wwZXZk\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721.sol\":{\"keccak256\":\"0x5dc63d1c6a12fe1b17793e1745877b2fcbe1964c3edfd0a482fac21ca8f18261\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6b7f97c5960a50fd1822cb298551ffc908e37b7893a68d6d08bce18a11cb0f11\",\"dweb:/ipfs/QmQQvxBytoY1eBt3pRQDmvH2hZ2yjhs12YqVfzGm7KSURq\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol\":{\"keccak256\":\"0x5191f783af281c75b7de0f1e3e36cdc6ac5cb2358d929584c4953fd02fa2b5eb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3ca2689d95ba45e297e55c8f71112e3ccec701d0087cb5e1c6ecb1b9ce86f00\",\"dweb:/ipfs/QmNQ5xKxJpF9k7AahnmJYvg5XeGSYtRig2Lp2WHmWXyBze\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/extensions/IERC721Enumerable.sol\":{\"keccak256\":\"0x3d6954a93ac198a2ffa384fa58ccf18e7e235263e051a394328002eff4e073de\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f58c799bd939d3951c94893e83ef86acd56989d1d7db7f9d180c515e29e28ff\",\"dweb:/ipfs/QmTgAxHAAys4kq9ZfU9YB24MWYoHLGAKSxnYUigPFrNW7g\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol\":{\"keccak256\":\"0x37d1aaaa5a2908a09e9dcf56a26ddf762ecf295afb5964695937344fc6802ce1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed0bfc1b92153c5000e50f4021367b931bbe96372ac6facec3c4961b72053d02\",\"dweb:/ipfs/Qmbwp8VDerjS5SV1quwHH1oMXxPQ93fzfLVqJ2RCqbowGE\"]},\"node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol\":{\"keccak256\":\"0xddab643169f47a2c5291afafcbfdca045d9e6835553307d090bc048b6dabd0ac\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d0ffbacfee42977167b3c75bd4787f8b72a7ab1176abd544f3dff662c6528e24\",\"dweb:/ipfs/QmUprM1cWCyaQ3LDjHA2DhwiPs3wekQ6MWXHFZdMMxpcyX\"]},\"node_modules/@openzeppelin/contracts/utils/Base64.sol\":{\"keccak256\":\"0xbee2b819e1b4bf569ffc1b1b9d560b4abd6a589575f3093edaab9244de18a0c2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e478c0e9bbf3eb8cd3b7784f9b397603e34747f9ffd16571ed1d89ce102de389\",\"dweb:/ipfs/QmZ6zXpwy5xRxx9RkodJmDZSUTeEqPQUanAC5TUoYqW2VR\"]},\"node_modules/@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"node_modules/@openzeppelin/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"node_modules/@openzeppelin/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"node_modules/@openzeppelin/contracts/utils/Strings.sol\":{\"keccak256\":\"0x81c274a60a7ae232ae3dc9ff3a4011b4849a853c13b0832cd3351bb1bb2f0dae\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9da0c20dc74358a2a76330818f3bac9d1e2ce3371aec847b9cbf5d147fbae4f6\",\"dweb:/ipfs/QmeczhmnFv1hbXKGLwbYXY6Rrytc9a5A2YaRi5QMMgjPnb\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287\",\"dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"node_modules/@openzeppelin/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"node_modules/@openzeppelin/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"node_modules/solady/src/utils/EIP712.sol\":{\"keccak256\":\"0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d\",\"dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM\"]},\"node_modules/solady/src/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0xdb28f318ec45197a6c7cc2abebed67d7cb8b965838ef962e3844423256a9ddb8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://873cd46b77a2aeb781e7a0d131e7299151323ed884c330101a51d0727e218d98\",\"dweb:/ipfs/QmddadCjyedztvdSgLZEyKWoRes2SqtpviSjhEbSNrkUoi\"]},\"node_modules/solady/src/utils/SignatureCheckerLib.sol\":{\"keccak256\":\"0x472ad43dd67dcc0d548efbbc8779f34e56a7eb99d7a8b1c29af33fb03bbe9ffc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://733da5015058b9540927c63c316665e9ac8d9676d23f722bddf9376c2a03d4cb\",\"dweb:/ipfs/QmPhXj1qpjnwVjrZUgFdkinMK9XSuoLN3cxbSmqADYSZry\"]},\"src/consensus/ConsensusRegistry.sol\":{\"keccak256\":\"0x092ea91b70605cd7c31bd4e26fae5ab662cda12264bc647988e2bba5a3c004f9\",\"license\":\"MIT or Apache-2.0\",\"urls\":[\"bzz-raw://e12aac5836cad83498a793319d85b2e693cdc2b16484d59f5965b390ac109750\",\"dweb:/ipfs/QmNabNMFxu2fBymogNTFjejTChnyhwyyGWFbdeXpNHJP51\"]},\"src/consensus/Issuance.sol\":{\"keccak256\":\"0x828e63b29c25ac84426be4ae2160b769dad52f3d094bebf60cd408bfbc98ba9b\",\"license\":\"MIT or Apache-2.0\",\"urls\":[\"bzz-raw://3014b251ab14d10de79f6a8aaaff5d95f946f7b65ab91600899ef64328900fa6\",\"dweb:/ipfs/QmTSPsJcp9W2KYpGPomESHosXSLBbzgMe2QVkJDUE5uWFZ\"]},\"src/consensus/StakeManager.sol\":{\"keccak256\":\"0x9f6bad28600a85ee6ac58d26db9200a84f4cb0723176bdd1853e285250d15c88\",\"license\":\"MIT or Apache-2.0\",\"urls\":[\"bzz-raw://fbb614ef25d64973b0cc6da99ddbf791265735d9e80abf4581726f3147e9ce6d\",\"dweb:/ipfs/QmQwZQ4dsUcvch4wRkBxgqdS29JeSUcJGmdQtJYt9aB4YF\"]},\"src/consensus/SystemCallable.sol\":{\"keccak256\":\"0x3847c04638c93949f5b2a6249d87a937d966c3066462313f9966080e2bf2d96e\",\"license\":\"MIT or Apache-2.0\",\"urls\":[\"bzz-raw://38885a9c0741792a3b687e72a901fbbcbf5436656b88e98dbb9bd9699ba7c156\",\"dweb:/ipfs/QmcnUbBTF6hYoCYQ319PLD2B2tasNKjcqH7da72iDHjGfU\"]},\"src/interfaces/IConsensusRegistry.sol\":{\"keccak256\":\"0x393e55b64e1494d3504615b97d8fbdb5c97ab2b0566bc654a030fdb4d1ddb7cd\",\"license\":\"MIT or Apache-2.0\",\"urls\":[\"bzz-raw://f342779cb0826e48843803b288ff3c6236b6bd771d9aa72d97b1894ae4fea7fd\",\"dweb:/ipfs/QmewJqUp75jbcLbhy25S9eFYCNCco3a92hMZ8qCuRVtLuU\"]},\"src/interfaces/IStakeManager.sol\":{\"keccak256\":\"0x375a2d1950e7b5ad826a1ec4413f5e1303865327763731502e41349517c68db4\",\"license\":\"MIT or Apache-2.0\",\"urls\":[\"bzz-raw://31f5338606286d1a286091f98f78da705cedb9e4a10e0499b55072bdcb7d6f3a\",\"dweb:/ipfs/QmZ5PqYNTfesKMhqYikJ9QRybG5grXbEcPKjiriDFJ1GiM\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct IStakeManager.StakeConfig", "name": "genesisConfig_", "type": "tuple", "components": [{"internalType": "uint256", "name": "stakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minWithdrawAmount", "type": "uint256"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}]}, {"internalType": "struct IConsensusRegistry.ValidatorInfo[]", "name": "initialValidators_", "type": "tuple[]", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}, {"internalType": "address", "name": "owner_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "type": "error", "name": "AlreadyDefined"}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "type": "error", "name": "CommitteeRequirement"}, {"inputs": [], "type": "error", "name": "DuplicateBLSPubkey"}, {"inputs": [], "type": "error", "name": "ERC721EnumerableForbiddenBatchMint"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC721IncorrectOwner"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "type": "error", "name": "ERC721InsufficientApproval"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC721InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "type": "error", "name": "ERC721InvalidOperator"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC721InvalidOwner"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC721InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC721InvalidSender"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "type": "error", "name": "ERC721NonexistentToken"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "type": "error", "name": "ERC721OutOfBoundsIndex"}, {"inputs": [], "type": "error", "name": "EnforcedPause"}, {"inputs": [], "type": "error", "name": "ExpectedPause"}, {"inputs": [], "type": "error", "name": "GenesisArityMismatch"}, {"inputs": [{"internalType": "uint256", "name": "withdrawAmount", "type": "uint256"}], "type": "error", "name": "InsufficientRewards"}, {"inputs": [], "type": "error", "name": "InvalidBLSPubkey"}, {"inputs": [{"internalType": "uint256", "name": "minCommitteeSize", "type": "uint256"}, {"internalType": "uint256", "name": "providedCommitteeSize", "type": "uint256"}], "type": "error", "name": "InvalidCommitteeSize"}, {"inputs": [{"internalType": "uint32", "name": "duration", "type": "uint32"}], "type": "error", "name": "InvalidDuration"}, {"inputs": [{"internalType": "uint32", "name": "epoch", "type": "uint32"}], "type": "error", "name": "InvalidEpoch"}, {"inputs": [], "type": "error", "name": "InvalidProof"}, {"inputs": [{"internalType": "uint256", "name": "stakeAmount", "type": "uint256"}], "type": "error", "name": "InvalidStakeAmount"}, {"inputs": [{"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "status", "type": "uint8"}], "type": "error", "name": "InvalidStatus"}, {"inputs": [], "type": "error", "name": "InvalidSupply"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "type": "error", "name": "InvalidTokenId"}, {"inputs": [], "type": "error", "name": "InvalidValidatorAddress"}, {"inputs": [], "type": "error", "name": "LowLevelCallFailure"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "type": "error", "name": "NotRecipient"}, {"inputs": [], "type": "error", "name": "NotTransferable"}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "type": "error", "name": "NotValidator"}, {"inputs": [{"internalType": "address", "name": "invalidCaller", "type": "address"}], "type": "error", "name": "OnlySystemCall"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "Reentrancy"}, {"inputs": [], "type": "error", "name": "RequiresConsensusNFT"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "StringsInsufficientHexLength"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "approved", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenId", "type": "uint256", "indexed": true}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "approved", "type": "bool", "indexed": false}], "type": "event", "name": "ApprovalForAll", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.EpochInfo", "name": "epoch", "type": "tuple", "components": [{"internalType": "address[]", "name": "committee", "type": "address[]"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint64", "name": "blockHeight", "type": "uint64"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "NewEpoch", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Paused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "claimant", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "rewards", "type": "uint256", "indexed": false}], "type": "event", "name": "RewardsClaimed", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "tokenId", "type": "uint256", "indexed": true}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Unpaused", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "validator", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "ValidatorActivated", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "validator", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "ValidatorExited", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "validator", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "ValidatorPendingActivation", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "validator", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "ValidatorPendingExit", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "validator", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "ValidatorRetired", "anonymous": false}, {"inputs": [{"internalType": "struct Slash", "name": "slash", "type": "tuple", "components": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "indexed": false}], "type": "event", "name": "ValidatorSlashed", "anonymous": false}, {"inputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "validator", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}], "indexed": false}], "type": "event", "name": "ValidatorStaked", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SYSTEM_ADDRESS", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "activate"}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "allocateIssuance"}, {"inputs": [{"internalType": "struct RewardInfo[]", "name": "rewardInfos", "type": "tuple[]", "components": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "consensusHeaderCount", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "applyIncentives"}, {"inputs": [{"internalType": "struct Slash[]", "name": "slashes", "type": "tuple[]", "components": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "applySlashes"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "beginExit"}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimStakeRewards"}, {"inputs": [{"internalType": "address[]", "name": "futureCommittee", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "concludeEpoch"}, {"inputs": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes", "name": "validatorSig", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "delegateStake"}, {"inputs": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "delegator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "delegationDigest", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "epochInfo", "outputs": [{"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint64", "name": "blockHeight", "type": "uint64"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "futureEpochInfo", "outputs": [{"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint64", "name": "blockHeight", "type": "uint64"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32", "name": "epoch", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getCommitteeValidators", "outputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo[]", "name": "", "type": "tuple[]", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentEpoch", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentEpochInfo", "outputs": [{"internalType": "struct IConsensusRegistry.EpochInfo", "name": "", "type": "tuple", "components": [{"internalType": "address[]", "name": "committee", "type": "address[]"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint64", "name": "blockHeight", "type": "uint64"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentStakeConfig", "outputs": [{"internalType": "struct IStakeManager.StakeConfig", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "stakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minWithdrawAmount", "type": "uint256"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentStakeVersion", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "uint32", "name": "epoch", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getEpochInfo", "outputs": [{"internalType": "struct IConsensusRegistry.EpochInfo", "name": "", "type": "tuple", "components": [{"internalType": "address[]", "name": "committee", "type": "address[]"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint64", "name": "blockHeight", "type": "uint64"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}]}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getRewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getValidator", "outputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo", "name": "", "type": "tuple", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}]}, {"inputs": [{"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "status", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "getValidators", "outputs": [{"internalType": "struct IConsensusRegistry.ValidatorInfo[]", "name": "", "type": "tuple[]", "components": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isRetired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "issuance", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pause"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApprovalForAll"}, {"inputs": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "stake"}, {"inputs": [{"internalType": "uint8", "name": "version", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "stakeConfig", "outputs": [{"internalType": "struct IStakeManager.StakeConfig", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "stakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minWithdrawAmount", "type": "uint256"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}]}]}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokenByIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokenOfOwnerByIndex", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unpause"}, {"inputs": [{"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "unstake"}, {"inputs": [{"internalType": "struct IStakeManager.StakeConfig", "name": "newConfig", "type": "tuple", "components": [{"internalType": "uint256", "name": "stakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minWithdrawAmount", "type": "uint256"}, {"internalType": "uint256", "name": "epochIssuance", "type": "uint256"}, {"internalType": "uint32", "name": "epochDuration", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "upgradeStakeVersion", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "validators", "outputs": [{"internalType": "bytes", "name": "bls<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "address", "name": "validator<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint32", "name": "activationEpoch", "type": "uint32"}, {"internalType": "uint32", "name": "exitEpoch", "type": "uint32"}, {"internalType": "enum IConsensusRegistry.ValidatorStatus", "name": "currentStatus", "type": "uint8"}, {"internalType": "bool", "name": "isRetired", "type": "bool"}, {"internalType": "bool", "name": "isDelegated", "type": "bool"}, {"internalType": "uint8", "name": "stakeVersion", "type": "uint8"}]}], "devdoc": {"kind": "dev", "methods": {"activate()": {"details": "Self-activation function for validators, gaining `PendingActivation` status and setting next epoch as activation epoch to ensure rewards eligibility only after completing a full epoch"}, "allocateIssuance()": {"details": "Permissioned function to allocate TEL for epoch issuance, ie consensus block rewards"}, "applyIncentives((address,uint256)[])": {"details": "The network's epoch issuance distribution method, rewarding stake originators based on initial stake and on the validator's performance (consensus header count)"}, "applySlashes((address,uint256)[])": {"details": "The network's slashing mechanism, which penalizes validators for misbehaving"}, "balanceOf(address)": {"details": "See {IERC721-balanceOf}."}, "beginExit()": {"details": "Issues an exit request for a validator to be retired from the `Active` validator set"}, "burn(address)": {"details": "In the case of malicious or erroneous node operator behavior, governance can use this function to burn a validator's `ConsensusNFT` and immediately eject from consensus committees if applicableIntended for sparing use; only reverts if burning results in empty committee", "params": {"from": "Refers to the struct member `ValidatorInfo.validatorAddress` in `IConsensusRegistry`"}}, "claimStakeRewards(address)": {"details": "Used by rewardees to claim staking rewards"}, "concludeEpoch(address[])": {"details": "Accepts the committee of voting validators for 2 epochs in the future", "params": {"newCommittee": "The future validator committee for `$.currentEpoch + 3`"}}, "constructor": {"details": "Stake for `initialValidators_` is allocated directly to the ConsensusRegistry balance and decremented directly from InterchainTEL within the protocol on the rust sideOnly governance delegation is enabled at genesis", "params": {"initialValidators_": "The initial validator set running Telcoin Network; these validators will comprise the voter committee for the first three epochs, ie `epochInfo[0:2]`"}}, "delegateStake(bytes,address,bytes)": {"details": "Accepts delegated stake from a non-validator caller authorized by a validator's EIP712 signature"}, "delegationDigest(bytes,address,address)": {"returns": {"_0": "_ EIP-712 typed struct hash used to enable delegated proof of stake"}}, "eip712Domain()": {"details": "See: https://eips.ethereum.org/EIPS/eip-5267"}, "getApproved(uint256)": {"details": "See {IERC721-getApproved}."}, "getBalance(address)": {"details": "Returns staking information for the given address"}, "getCommitteeValidators(uint32)": {"details": "Fetches the committee for a given epoch"}, "getCurrentEpoch()": {"details": "Returns the current epoch"}, "getCurrentEpochInfo()": {"details": "Returns the current epoch's committee and block height"}, "getCurrentStakeConfig()": {"details": "Returns the current stake configuration"}, "getCurrentStakeVersion()": {"details": "Returns the current version"}, "getEpochInfo(uint32)": {"details": "Returns information about the provided epoch. Only four latest & two future epochs are stored"}, "getRewards(address)": {"details": "Fetches the claimable rewards accrued for a given validator address", "returns": {"_0": "_ The validator's claimable rewards, not including the validator's stake"}}, "getValidator(address)": {"details": "Fetches the `ValidatorInfo` for a given `validatorAddress == ConsensusNFT tokenId`"}, "getValidators(uint8)": {"details": "Returns an array of unretired validators matching the provided status", "params": {"": "`Active` queries also include validators pending activation or exit since all three remain eligible for committee service in the next epoch"}}, "isApprovedForAll(address,address)": {"details": "See {IERC721-isApprovedForAll}."}, "isRetired(address)": {"details": "Returns whether a validator is exited && unstaked, ie \"retired\""}, "mint(address)": {"details": "The StakeManager's ERC721 ledger serves a permissioning role over validators, requiring Telcoin governance to approve each node operator and manually issue them a `ConsensusNFT`", "params": {"to": "Refers to the struct member `ValidatorInfo.validatorAddress` in `IConsensusRegistry`"}}, "name()": {"details": "See {IERC721Metadata-name}."}, "owner()": {"details": "Returns the address of the current owner."}, "ownerOf(uint256)": {"details": "See {IERC721-ownerOf}."}, "pause()": {"details": "Emergency function to pause validator and stake management"}, "paused()": {"details": "Returns true if the contract is paused, and false otherwise."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "safeTransferFrom(address,address,uint256)": {"details": "See {IERC721-safeTransferFrom}."}, "safeTransferFrom(address,address,uint256,bytes)": {"details": "See {IERC721-safeTransferFrom}."}, "stake(bytes)": {"details": "Accepts the native TEL stake amount from the calling validator, enabling later self-activation"}, "stakeConfig(uint8)": {"details": "Returns the queried stake configuration"}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "symbol()": {"details": "See {IERC721Metadata-symbol}."}, "tokenByIndex(uint256)": {"details": "See {IERC721Enumerable-tokenByIndex}."}, "tokenOfOwnerByIndex(address,uint256)": {"details": "See {IERC721Enumerable-tokenOfOwnerByIndex}."}, "totalSupply()": {"details": "See {IERC721Enumerable-totalSupply}."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "unpause()": {"details": "Emergency function to unpause validator and stake management"}, "unstake(address)": {"details": "Returns previously staked funds in addition to accrued rewards, if any, to the staker"}, "upgradeStakeVersion((uint256,uint256,uint256,uint32))": {"details": "Permissioned function to upgrade stake, withdrawal, and consensus block reward configurations"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"activate()": {"notice": "Caller must own a ConsensusNFT and be `Staked` status, ie staked or delegated"}, "allocateIssuance()": {"notice": "Allocated TEL cannot be recovered; it is effectively burned cryptographically The only way received TEL can be re-minted is as staking issuance rewardsOnly governance may burn TEL in this manner"}, "applyIncentives((address,uint256)[])": {"notice": "Stake originators are either a delegator if one exists, or the validator itselfCalled just before concluding the current epochNot yet enabled during pilot, but scaffolding is included here. For the time being, system calls to this fn can provide empty calldata arrays"}, "applySlashes((address,uint256)[])": {"notice": "Called just before concluding the current epochNot yet enabled during pilot, but scaffolding is included here. For the time being, system calls to this fn can provide empty calldata arrays"}, "approve(address,uint256)": {"notice": "Wouldn't do anything because transfers are disabled but explicitly disallow anyway"}, "beginExit()": {"notice": "Reverts if the exit queue is full, ie if active validator count would drop too low"}, "concludeEpoch(address[])": {"notice": "Voting Validator Committee changes at the end every epoch via syscall"}, "delegateStake(bytes,address,bytes)": {"notice": "`validatorAddress` must be a validator already in possession of a `ConsensusNFT`"}, "delegationDigest(bytes,address,address)": {"notice": "Returns the delegation digest that a validator should sign to accept a delegation"}, "getEpochInfo(uint32)": {"notice": "When querying for future epochs, `blockHeight` will be 0 as they are not yet known"}, "isRetired(address)": {"notice": "After retiring, a validator's `tokenId == validatorAddress` cannot be reused"}, "mint(address)": {"notice": "For each mintee, `tokenId == uint160(to)`Access-gated in ConsensusRegistry to its owner, which is a Telcoin governance address"}, "pause()": {"notice": "Does not pause system callable or ConsensusNFT fns. Only accessible by `owner`"}, "setApprovalForAll(address,bool)": {"notice": "Wouldn't do anything because transfers are disabled but explicitly disallow anyway"}, "stake(bytes)": {"notice": "Caller must already have been issued a `ConsensusNFT` by Telcoin governance"}, "tokenURI(uint256)": {"notice": "Read-only mechanism, not yet live"}, "transferFrom(address,address,uint256)": {"notice": "Consensus NFTs are soulbound to validators and cannot be transferred unless burned"}, "unpause()": {"notice": "Does not affect system callable or ConsensusNFT fns. Only accessible by `owner`"}, "unstake(address)": {"notice": "May be used to reverse validator onboarding pre-activation or permanently retire after full exitOnce unstaked and retired, validator addresses cannot be reused"}, "upgradeStakeVersion((uint256,uint256,uint256,uint32))": {"notice": "The new version takes effect in the next epoch"}}, "version": 1}}, "settings": {"remappings": ["@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/", "@axelar-network/=node_modules/@axelar-network/", "@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/", "@openzeppelin/=node_modules/@openzeppelin/", "@uniswap/=node_modules/@uniswap/", "ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/", "forge-std/=node_modules/forge-std/src/", "recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/", "solady/=node_modules/solady/src/", "telcoin-contracts/=node_modules/telcoin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"src/consensus/ConsensusRegistry.sol": "ConsensusRegistry"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b", "urls": ["bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b", "dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol": {"keccak256": "0x39ed367e54765186281efcfe83e47cf0ad62cc879f10e191360712507125f29a", "urls": ["bzz-raw://2c5ae6d85bd48cca8d6d2fcec8c63efd86f56f8a5832577a47e403ce0e65cb09", "dweb:/ipfs/QmUtcS8AbRSWhuc61puYet58os8FvSqm329ChoW8wwZXZk"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721.sol": {"keccak256": "0x5dc63d1c6a12fe1b17793e1745877b2fcbe1964c3edfd0a482fac21ca8f18261", "urls": ["bzz-raw://6b7f97c5960a50fd1822cb298551ffc908e37b7893a68d6d08bce18a11cb0f11", "dweb:/ipfs/QmQQvxBytoY1eBt3pRQDmvH2hZ2yjhs12YqVfzGm7KSURq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol": {"keccak256": "0x5191f783af281c75b7de0f1e3e36cdc6ac5cb2358d929584c4953fd02fa2b5eb", "urls": ["bzz-raw://d3ca2689d95ba45e297e55c8f71112e3ccec701d0087cb5e1c6ecb1b9ce86f00", "dweb:/ipfs/QmNQ5xKxJpF9k7AahnmJYvg5XeGSYtRig2Lp2WHmWXyBze"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/extensions/IERC721Enumerable.sol": {"keccak256": "0x3d6954a93ac198a2ffa384fa58ccf18e7e235263e051a394328002eff4e073de", "urls": ["bzz-raw://1f58c799bd939d3951c94893e83ef86acd56989d1d7db7f9d180c515e29e28ff", "dweb:/ipfs/QmTgAxHAAys4kq9ZfU9YB24MWYoHLGAKSxnYUigPFrNW7g"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol": {"keccak256": "0x37d1aaaa5a2908a09e9dcf56a26ddf762ecf295afb5964695937344fc6802ce1", "urls": ["bzz-raw://ed0bfc1b92153c5000e50f4021367b931bbe96372ac6facec3c4961b72053d02", "dweb:/ipfs/Qmbwp8VDerjS5SV1quwHH1oMXxPQ93fzfLVqJ2RCqbowGE"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol": {"keccak256": "0xddab643169f47a2c5291afafcbfdca045d9e6835553307d090bc048b6dabd0ac", "urls": ["bzz-raw://d0ffbacfee42977167b3c75bd4787f8b72a7ab1176abd544f3dff662c6528e24", "dweb:/ipfs/QmUprM1cWCyaQ3LDjHA2DhwiPs3wekQ6MWXHFZdMMxpcyX"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Base64.sol": {"keccak256": "0xbee2b819e1b4bf569ffc1b1b9d560b4abd6a589575f3093edaab9244de18a0c2", "urls": ["bzz-raw://e478c0e9bbf3eb8cd3b7784f9b397603e34747f9ffd16571ed1d89ce102de389", "dweb:/ipfs/QmZ6zXpwy5xRxx9RkodJmDZSUTeEqPQUanAC5TUoYqW2VR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Strings.sol": {"keccak256": "0x81c274a60a7ae232ae3dc9ff3a4011b4849a853c13b0832cd3351bb1bb2f0dae", "urls": ["bzz-raw://9da0c20dc74358a2a76330818f3bac9d1e2ce3371aec847b9cbf5d147fbae4f6", "dweb:/ipfs/QmeczhmnFv1hbXKGLwbYXY6Rrytc9a5A2YaRi5QMMgjPnb"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/ERC165.sol": {"keccak256": "0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa", "urls": ["bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287", "dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "node_modules/solady/src/utils/EIP712.sol": {"keccak256": "0xb5c4c8ac5368c9785b4e30314f4ad6f3ae13bdc21679007735681d13da797bec", "urls": ["bzz-raw://c4456a4eaa8748f802fd1188db6405d18c452eb7c0dde84a49b49a7f94b5970d", "dweb:/ipfs/QmZzsFn4VwvBFy2MJVJXvntCQsDRCXbRrSKKfXxXv9jYGM"], "license": "MIT"}, "node_modules/solady/src/utils/ReentrancyGuard.sol": {"keccak256": "0xdb28f318ec45197a6c7cc2abebed67d7cb8b965838ef962e3844423256a9ddb8", "urls": ["bzz-raw://873cd46b77a2aeb781e7a0d131e7299151323ed884c330101a51d0727e218d98", "dweb:/ipfs/QmddadCjyedztvdSgLZEyKWoRes2SqtpviSjhEbSNrkUoi"], "license": "MIT"}, "node_modules/solady/src/utils/SignatureCheckerLib.sol": {"keccak256": "0x472ad43dd67dcc0d548efbbc8779f34e56a7eb99d7a8b1c29af33fb03bbe9ffc", "urls": ["bzz-raw://733da5015058b9540927c63c316665e9ac8d9676d23f722bddf9376c2a03d4cb", "dweb:/ipfs/QmPhXj1qpjnwVjrZUgFdkinMK9XSuoLN3cxbSmqADYSZry"], "license": "MIT"}, "src/consensus/ConsensusRegistry.sol": {"keccak256": "0x092ea91b70605cd7c31bd4e26fae5ab662cda12264bc647988e2bba5a3c004f9", "urls": ["bzz-raw://e12aac5836cad83498a793319d85b2e693cdc2b16484d59f5965b390ac109750", "dweb:/ipfs/QmNabNMFxu2fBymogNTFjejTChnyhwyyGWFbdeXpNHJP51"], "license": "MIT or Apache-2.0"}, "src/consensus/Issuance.sol": {"keccak256": "0x828e63b29c25ac84426be4ae2160b769dad52f3d094bebf60cd408bfbc98ba9b", "urls": ["bzz-raw://3014b251ab14d10de79f6a8aaaff5d95f946f7b65ab91600899ef64328900fa6", "dweb:/ipfs/QmTSPsJcp9W2KYpGPomESHosXSLBbzgMe2QVkJDUE5uWFZ"], "license": "MIT or Apache-2.0"}, "src/consensus/StakeManager.sol": {"keccak256": "0x9f6bad28600a85ee6ac58d26db9200a84f4cb0723176bdd1853e285250d15c88", "urls": ["bzz-raw://fbb614ef25d64973b0cc6da99ddbf791265735d9e80abf4581726f3147e9ce6d", "dweb:/ipfs/QmQwZQ4dsUcvch4wRkBxgqdS29JeSUcJGmdQtJYt9aB4YF"], "license": "MIT or Apache-2.0"}, "src/consensus/SystemCallable.sol": {"keccak256": "0x3847c04638c93949f5b2a6249d87a937d966c3066462313f9966080e2bf2d96e", "urls": ["bzz-raw://38885a9c0741792a3b687e72a901fbbcbf5436656b88e98dbb9bd9699ba7c156", "dweb:/ipfs/QmcnUbBTF6hYoCYQ319PLD2B2tasNKjcqH7da72iDHjGfU"], "license": "MIT or Apache-2.0"}, "src/interfaces/IConsensusRegistry.sol": {"keccak256": "0x393e55b64e1494d3504615b97d8fbdb5c97ab2b0566bc654a030fdb4d1ddb7cd", "urls": ["bzz-raw://f342779cb0826e48843803b288ff3c6236b6bd771d9aa72d97b1894ae4fea7fd", "dweb:/ipfs/QmewJqUp75jbcLbhy25S9eFYCNCco3a92hMZ8qCuRVtLuU"], "license": "MIT or Apache-2.0"}, "src/interfaces/IStakeManager.sol": {"keccak256": "0x375a2d1950e7b5ad826a1ec4413f5e1303865327763731502e41349517c68db4", "urls": ["bzz-raw://31f5338606286d1a286091f98f78da705cedb9e4a10e0499b55072bdcb7d6f3a", "dweb:/ipfs/QmZ5PqYNTfesKMhqYikJ9QRybG5grXbEcPKjiriDFJ1GiM"], "license": "MIT or Apache-2.0"}}, "version": 1}, "id": 204}