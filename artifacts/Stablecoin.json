{"abi": [{"type": "function", "name": "BLACKLISTER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "BURNER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MINTER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SUPPORT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "addBlackList", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blacklisted", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burnFrom", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "erc20Rescue", "inputs": [{"name": "token", "type": "address", "internalType": "contract ERC20PermitUpgradeable"}, {"name": "destination", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "symbol_", "type": "string", "internalType": "string"}, {"name": "decimals_", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintTo", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeBlackList", "inputs": [{"name": "user", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "AddedBlacklist", "inputs": [{"name": "user", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "EIP712DomainChanged", "inputs": [], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "RemovedBlacklist", "inputs": [{"name": "user", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AlreadyBlacklisted", "inputs": [{"name": "user", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Blacklisted", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC2612ExpiredSignature", "inputs": [{"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2612InvalidSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "InvalidAccountNonce", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "currentNonce", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotBlacklisted", "inputs": [{"name": "user", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "823:4886:112:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "823:4886:112:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3443:202:32;;;;;;:::i;:::-;;:::i;:::-;;;470:14:142;;463:22;445:41;;433:2;418:18;3443:202:32;;;;;;;;3011:144:36;;;:::i;:::-;;;;;;;:::i;5505:186::-;;;;;;:::i;:::-;;:::i;2308:333:114:-;;;;;;:::i;:::-;;:::i;:::-;;2090:335:112;;;;;;:::i;:::-;;:::i;4191:152:36:-;4322:14;;4191:152;;;3796:25:142;;;3784:2;3769:18;4191:152:36;3650:177:142;6251:244:36;;;;;;:::i;:::-;;:::i;4759:191:32:-;;;;;;:::i;:::-;;:::i;1396:62:112:-;;1434:24;1396:62;;5246:136:32;;;;;;:::i;:::-;;:::i;2499:135:112:-;1232:66;2579:47;2499:135;;5302:4:142;5290:17;;;5272:36;;5260:2;5245:18;2499:135:112;5130:184:142;3082:112:37;;;:::i;6348:245:32:-;;;;;;:::i;:::-;;:::i;3677:101:112:-;;;;;;:::i;:::-;;:::i;3338:137::-;;;;;;:::i;:::-;;:::i;4401:171:36:-;;;;;;:::i;:::-;;:::i;4088:194:112:-;;;;;;:::i;:::-;;:::i;2821:154:37:-;;;;;;:::i;:::-;;:::i;5173:903:42:-;;;:::i;:::-;;;;;;;;;;;;;:::i;3732:207:32:-;;;;;;:::i;:::-;;:::i;1464:64:112:-;;1503:25;1464:64;;3268:148:36;;;:::i;2970:101:112:-;;;;;;:::i;:::-;;:::i;2317:49:32:-;;2362:4;2317:49;;4767:178:36;;;;;;:::i;:::-;;:::i;2095:672:37:-;;;;;;:::i;:::-;;:::i;1328:62:112:-;;1366:24;1328:62;;5662:138:32;;;;;;:::i;:::-;;:::i;5501:206:112:-;;;;;;:::i;:::-;;:::i;1983:165:114:-;;;;;;:::i;:::-;;:::i;5003:195:36:-;;;;;;:::i;:::-;;:::i;2800:236:114:-;;;;;;:::i;:::-;;:::i;1014:72::-;;1057:29;1014:72;;3443:202:32;3528:4;-1:-1:-1;;;;;;3551:47:32;;-1:-1:-1;;;3551:47:32;;:87;;-1:-1:-1;;;;;;;;;;1133:40:43;;;3602:36:32;3544:94;3443:202;-1:-1:-1;;3443:202:32:o;3011:144:36:-;3056:13;3081:22;-1:-1:-1;;;;;;;;;;;3106:18:36;3081:43;;3141:1;:7;;3134:14;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3011:144;:::o;5505:186::-;5578:4;966:10:39;5632:31:36;966:10:39;5648:7:36;5657:5;5632:8;:31::i;:::-;-1:-1:-1;5680:4:36;;5505:186;-1:-1:-1;;;5505:186:36:o;2308:333:114:-;1057:29;3191:16:32;3202:4;3191:10;:16::i;:::-;2414:17:114::1;2426:4;2414:11;:17::i;:::-;2410:54;;;2440:24;::::0;-1:-1:-1;;;2440:24:114;;-1:-1:-1;;;;;9226:32:142;;2440:24:114::1;::::0;::::1;9208:51:142::0;9181:18;;2440:24:114::1;;;;;;;;2410:54;2474:22;2491:4;2474:16;:22::i;:::-;2574:25;2588:4;2594;2574:13;:25::i;:::-;2614:20;::::0;-1:-1:-1;;;;;9226:32:142;;9208:51;;2614:20:114::1;::::0;9196:2:142;9181:18;2614:20:114::1;;;;;;;;2308:333:::0;;:::o;2090:335:112:-;8870:21:34;4302:15;;-1:-1:-1;;;4302:15:34;;;;4301:16;;4348:14;;4158:30;4726:16;;:34;;;;;4746:14;4726:34;4706:54;;4770:17;4790:11;:16;;4805:1;4790:16;:50;;;;-1:-1:-1;4818:4:34;4810:25;:30;4790:50;4770:70;;4856:12;4855:13;:30;;;;;4873:12;4872:13;4855:30;4851:91;;;4908:23;;-1:-1:-1;;;4908:23:34;;;;;;;;;;;4851:91;4951:18;;-1:-1:-1;;4951:18:34;4968:1;4951:18;;;4979:67;;;;5013:22;;-1:-1:-1;;;;5013:22:34;-1:-1:-1;;;5013:22:34;;;4979:67;2232:28:112::1;2245:5;2252:7;2232:12;:28::i;:::-;2270:25;2289:5;2270:18;:25::i;:::-;2305:44;2362:4:32;966:10:39::0;2305::112::1;:44::i;:::-;-1:-1:-1::0;2359:59:112::1;::::0;::::1;1232:66;2359:59:::0;5066:101:34;;;;5100:23;;-1:-1:-1;;;;5100:23:34;;;5142:14;;-1:-1:-1;9423:50:142;;5142:14:34;;9411:2:142;9396:18;5142:14:34;;;;;;;5066:101;4092:1081;;;;;2090:335:112;;;:::o;6251:244:36:-;6338:4;966:10:39;6394:37:36;6410:4;966:10:39;6425:5:36;6394:15;:37::i;:::-;6441:26;6451:4;6457:2;6461:5;6441:9;:26::i;:::-;6484:4;6477:11;;;6251:244;;;;;;:::o;4759:191:32:-;4824:7;4919:14;;;-1:-1:-1;;;;;;;;;;;4919:14:32;;;;;:24;;;;4759:191::o;5246:136::-;5320:18;5333:4;5320:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5350:25:::1;5361:4;5367:7;5350:10;:25::i;:::-;;5246:136:::0;;;:::o;3082:112:37:-;3141:7;3167:20;:18;:20::i;:::-;3160:27;;3082:112;:::o;6348:245:32:-;-1:-1:-1;;;;;6441:34:32;;966:10:39;6441:34:32;6437:102;;6498:30;;-1:-1:-1;;;6498:30:32;;;;;;;;;;;6437:102;6549:37;6561:4;6567:18;6549:11;:37::i;:::-;;6348:245;;:::o;3677:101:112:-;1434:24;3191:16:32;3202:4;3191:10;:16::i;:::-;3745:26:112::1;966:10:39::0;3765:5:112::1;3745;:26::i;:::-;3677:101:::0;;:::o;3338:137::-;1366:24;3191:16:32;3202:4;3191:10;:16::i;:::-;3447:21:112::1;3453:7;3462:5;3447;:21::i;4401:171:36:-:0;4466:7;;-1:-1:-1;;;;;;;;;;;4510:18:36;-1:-1:-1;;;;;4545:20:36;;;:11;:20;;;;;;;;-1:-1:-1;;4545:20:36;;;;;4401:171::o;4088:194:112:-;1434:24;3191:16:32;3202:4;3191:10;:16::i;:::-;4199:45:112::1;4215:7:::0;966:10:39;4238:5:112::1;4199:15;:45::i;:::-;4254:21;4260:7;4269:5;4254;:21::i;2821:154:37:-:0;2923:7;2949:19;2962:5;2949:12;:19::i;5173:903:42:-;5271:13;5298:18;;5271:13;;;5298:18;5271:13;-1:-1:-1;;;;;;;;;;;5777:13:42;;5511:45;;-1:-1:-1;5777:18:42;:43;;;;-1:-1:-1;5799:16:42;;;;:21;5777:43;5769:77;;;;-1:-1:-1;;;5769:77:42;;9686:2:142;5769:77:42;;;9668:21:142;9725:2;9705:18;;;9698:30;-1:-1:-1;;;9744:18:142;;;9737:51;9805:18;;5769:77:42;9484:345:142;5769:77:42;5908:13;:11;:13::i;:::-;5935:16;:14;:16::i;:::-;6043;;;6027:1;6043:16;;;;;;;;;-1:-1:-1;;;5857:212:42;;;-1:-1:-1;5857:212:42;;-1:-1:-1;5965:13:42;;-1:-1:-1;6000:4:42;;-1:-1:-1;6027:1:42;-1:-1:-1;6043:16:42;-1:-1:-1;5857:212:42;-1:-1:-1;;5173:903:42:o;3732:207:32:-;3809:4;3901:14;;;-1:-1:-1;;;;;;;;;;;3901:14:32;;;;;;;;-1:-1:-1;;;;;3901:31:32;;;;;;;;;;;;;;;3732:207::o;3268:148:36:-;3400:9;3393:16;;3315:13;;-1:-1:-1;;;;;;;;;;;2359:20:36;3393:16;;;:::i;2970:101:112:-;1366:24;3191:16:32;3202:4;3191:10;:16::i;:::-;3038:26:112::1;966:10:39::0;3058:5:112::1;3038;:26::i;4767:178:36:-:0;4836:4;966:10:39;4890:27:36;966:10:39;4907:2:36;4911:5;4890:9;:27::i;2095:672:37:-;2316:8;2298:15;:26;2294:97;;;2347:33;;-1:-1:-1;;;2347:33:37;;;;;3796:25:142;;;3769:18;;2347:33:37;3650:177:142;2294:97:37;2401:18;1277:95;2460:5;2467:7;2476:5;2483:16;2493:5;-1:-1:-1;;;;;1954:16:40;1597:7;1954:16;;;1005:21;1954:16;;;;;:18;;;;;;;;;1537:452;2483:16:37;2432:78;;;;;;10121:25:142;;;;-1:-1:-1;;;;;10182:32:142;;;10162:18;;;10155:60;10251:32;;;;10231:18;;;10224:60;10300:18;;;10293:34;10343:19;;;10336:35;10387:19;;;10380:35;;;10093:19;;2432:78:37;;;;;;;;;;;;2422:89;;;;;;2401:110;;2522:12;2537:28;2554:10;2537:16;:28::i;:::-;2522:43;;2576:14;2593:28;2607:4;2613:1;2616;2619;2593:13;:28::i;:::-;2576:45;;2645:5;-1:-1:-1;;;;;2635:15:37;:6;-1:-1:-1;;;;;2635:15:37;;2631:88;;2673:35;;-1:-1:-1;;;2673:35:37;;-1:-1:-1;;;;;10618:32:142;;;2673:35:37;;;10600:51:142;10687:32;;10667:18;;;10660:60;10573:18;;2673:35:37;10426:300:142;2631:88:37;2729:31;2738:5;2745:7;2754:5;2729:8;:31::i;:::-;2284:483;;;2095:672;;;;;;;:::o;5662:138:32:-;5737:18;5750:4;5737:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5767:26:::1;5779:4;5785:7;5767:11;:26::i;5501:206:112:-:0;1503:25;3191:16:32;3202:4;3191:10;:16::i;:::-;5661:39:112::1;-1:-1:-1::0;;;;;5661:18:112;::::1;5680:11:::0;5693:6;5661:18:::1;:39::i;1983:165:114:-:0;-1:-1:-1;;;;;2123:18:114;2039:4;2123:18;;;968:24;2123:18;;;;;;;;;1983:165::o;5003:195:36:-;-1:-1:-1;;;;;5162:20:36;;;5083:7;5162:20;;;:13;:20;;;;;;;;:29;;;;;;;;;;;;;5003:195::o;2800:236:114:-;1057:29;3191:16:32;3202:4;3191:10;:16::i;:::-;2910:17:114::1;2922:4;2910:11;:17::i;:::-;2905:51;;2936:20;::::0;-1:-1:-1;;;2936:20:114;;-1:-1:-1;;;;;9226:32:142;;2936:20:114::1;::::0;::::1;9208:51:142::0;9181:18;;2936:20:114::1;9062:203:142::0;2905:51:114::1;2966:26;2980:4;2986:5;2966:13;:26::i;:::-;3007:22;::::0;-1:-1:-1;;;;;9226:32:142;;9208:51;;3007:22:114::1;::::0;9196:2:142;9181:18;3007:22:114::1;9062:203:142::0;10264:128:36;10348:37;10357:5;10364:7;10373:5;10380:4;10348:8;:37::i;4148:103:32:-;4214:30;4225:4;966:10:39;4214::32;:30::i;:::-;4148:103;:::o;4697:121:112:-;4765:46;4775:4;966:10:39;4795:15:112;4805:4;4795:9;:15::i;:::-;4765:9;:46::i;3108:170:114:-;-1:-1:-1;;;;;3245:18:114;;;;3184:26;3245:18;;;968:24;3245:18;;;;;:26;;-1:-1:-1;;3245:26:114;;;;;;;;;;3108:170::o;2577:147:36:-;6931:20:34;:18;:20::i;:::-;2679:38:36::1;2702:5;2709:7;2679:22;:38::i;1829:125:37:-:0;6931:20:34;:18;:20::i;:::-;1913:34:37::1;1937:4;1913:34;;;;;;;;;;;;;-1:-1:-1::0;;;1913:34:37::1;;::::0;:23:::1;:34::i;7270:387:32:-:0;7347:4;-1:-1:-1;;;;;;;;;;;7437:22:32;7445:4;7451:7;7437;:22::i;:::-;7432:219;;7475:8;:14;;;;;;;;;;;-1:-1:-1;;;;;7475:31:32;;;;;;;;;:38;;-1:-1:-1;;7475:38:32;7509:4;7475:38;;;7559:12;966:10:39;;887:96;7559:12:32;-1:-1:-1;;;;;7532:40:32;7550:7;-1:-1:-1;;;;;7532:40:32;7544:4;7532:40;;;;;;;;;;7593:4;7586:11;;;;;7432:219;7635:5;7628:12;;;;;11993:477:36;12092:24;12119:25;12129:5;12136:7;12119:9;:25::i;:::-;12092:52;;-1:-1:-1;;12158:16:36;:37;12154:310;;12234:5;12215:16;:24;12211:130;;;12266:60;;-1:-1:-1;;;12266:60:36;;-1:-1:-1;;;;;10951:32:142;;12266:60:36;;;10933:51:142;11000:18;;;10993:34;;;11043:18;;;11036:34;;;10906:18;;12266:60:36;10731:345:142;12211:130:36;12382:57;12391:5;12398:7;12426:5;12407:16;:24;12433:5;12382:8;:57::i;6868:300::-;-1:-1:-1;;;;;6951:18:36;;6947:86;;6992:30;;-1:-1:-1;;;6992:30:36;;7019:1;6992:30;;;9208:51:142;9181:18;;6992:30:36;9062:203:142;6947:86:36;-1:-1:-1;;;;;7046:16:36;;7042:86;;7085:32;;-1:-1:-1;;;7085:32:36;;7114:1;7085:32;;;9208:51:142;9181:18;;7085:32:36;9062:203:142;7042:86:36;7137:24;7145:4;7151:2;7155:5;7137:7;:24::i;4015:109:42:-;4068:7;4094:23;:21;:23::i;7892:388:32:-;7970:4;-1:-1:-1;;;;;;;;;;;8059:22:32;8067:4;8073:7;8059;:22::i;:::-;8055:219;;;8131:5;8097:14;;;;;;;;;;;-1:-1:-1;;;;;8097:31:32;;;;;;;;;;:39;;-1:-1:-1;;8097:39:32;;;8155:40;966:10:39;;8097:14:32;;8155:40;;8131:5;8155:40;8216:4;8209:11;;;;;9522:206:36;-1:-1:-1;;;;;9592:21:36;;9588:89;;9636:30;;-1:-1:-1;;;9636:30:36;;9663:1;9636:30;;;9208:51:142;9181:18;;9636:30:36;9062:203:142;9588:89:36;9686:35;9694:7;9711:1;9715:5;9686:7;:35::i;8996:208::-;-1:-1:-1;;;;;9066:21:36;;9062:91;;9110:32;;-1:-1:-1;;;9110:32:36;;9139:1;9110:32;;;9208:51:142;9181:18;;9110:32:36;9062:203:142;:91:36;9162:35;9178:1;9182:7;9191:5;9162:7;:35::i;1259:164:40:-;1319:7;;1005:21;1364:19;886:156;6300:155:42;6441:7;6434:14;;6354:13;;-1:-1:-1;;;;;;;;;;;2839:21:42;6434:14;;;:::i;6682:161::-;6739:13;6764:23;-1:-1:-1;;;;;;;;;;;6790:19:42;2720:156;4946:176;5023:7;5049:66;5082:20;:18;:20::i;:::-;5104:10;3555:4:66;3549:11;-1:-1:-1;;;3573:23:66;;3625:4;3616:14;;3609:39;;;;3677:4;3668:14;;3661:34;3733:4;3718:20;;;3353:401;6803:260:65;6888:7;6908:17;6927:18;6947:16;6967:25;6978:4;6984:1;6987;6990;6967:10;:25::i;:::-;6907:85;;;;;;7002:28;7014:5;7021:8;7002:11;:28::i;:::-;-1:-1:-1;7047:9:65;;6803:260;-1:-1:-1;;;;;;6803:260:65:o;1303:160:57:-;1412:43;;;-1:-1:-1;;;;;11273:32:142;;1412:43:57;;;11255:51:142;11322:18;;;;11315:34;;;1412:43:57;;;;;;;;;;11228:18:142;;;;1412:43:57;;;;;;;;-1:-1:-1;;;;;1412:43:57;-1:-1:-1;;;1412:43:57;;;1385:71;;1405:5;;1385:19;:71::i;11224:487:36:-;-1:-1:-1;;;;;;;;;;;;;;;;11389:19:36;;11385:89;;11431:32;;-1:-1:-1;;;11431:32:36;;11460:1;11431:32;;;9208:51:142;9181:18;;11431:32:36;9062:203:142;11385:89:36;-1:-1:-1;;;;;11487:21:36;;11483:90;;11531:31;;-1:-1:-1;;;11531:31:36;;11559:1;11531:31;;;9208:51:142;9181:18;;11531:31:36;9062:203:142;11483:90:36;-1:-1:-1;;;;;11582:20:36;;;;;;;:13;;;:20;;;;;;;;:29;;;;;;;;;:37;;;11629:76;;;;11679:7;-1:-1:-1;;;;;11663:31:36;11672:5;-1:-1:-1;;;;;11663:31:36;;11688:5;11663:31;;;;3796:25:142;;3784:2;3769:18;;3650:177;11663:31:36;;;;;;;;11629:76;11322:389;11224:487;;;;:::o;4381:197:32:-;4469:22;4477:4;4483:7;4469;:22::i;:::-;4464:108;;4514:47;;-1:-1:-1;;;4514:47:32;;-1:-1:-1;;;;;11273:32:142;;4514:47:32;;;11255:51:142;11322:18;;;11315:34;;;11228:18;;4514:47:32;11081:274:142;7084:141:34;8870:21;8560:40;-1:-1:-1;;;8560:40:34;;;;7146:73;;7191:17;;-1:-1:-1;;;7191:17:34;;;;;;;;;;;7146:73;7084:141::o;2730:216:36:-;6931:20:34;:18;:20::i;:::-;-1:-1:-1;;;;;;;;;;;2895:7:36;:15:::1;2905:5:::0;2895:7;:15:::1;:::i;:::-;-1:-1:-1::0;2920:9:36::1;::::0;::::1;:19;2932:7:::0;2920:9;:19:::1;:::i;3599:330:42:-:0;6931:20:34;:18;:20::i;:::-;-1:-1:-1;;;;;;;;;;;3766:7:42;:14:::1;3776:4:::0;3766:7;:14:::1;:::i;:::-;-1:-1:-1::0;3790:10:42::1;::::0;::::1;:20;3803:7:::0;3790:10;:20:::1;:::i;:::-;-1:-1:-1::0;3891:1:42::1;3875:17:::0;;;3902:16:::1;::::0;;::::1;:20:::0;-1:-1:-1;;3599:330:42:o;4824:264:112:-;4944:17;4956:4;4944:11;:17::i;:::-;4940:47;;;4970:17;;-1:-1:-1;;;;;;4970:17:112;;-1:-1:-1;;;;;9226:32:142;;4970:17:112;;;9208:51:142;9181:18;;4970:17:112;9062:203:142;4940:47:112;5001:15;5013:2;5001:11;:15::i;:::-;4997:43;;;5025:15;;-1:-1:-1;;;;;;5025:15:112;;-1:-1:-1;;;;;9226:32:142;;5025:15:112;;;9208:51:142;9181:18;;5025:15:112;9062:203:142;4997:43:112;5051:30;5065:4;5071:2;5075:5;5051:13;:30::i;4130:191:42:-;4185:7;2073:95;4243:17;:15;:17::i;:::-;4262:20;:18;:20::i;:::-;4221:92;;;;;;14022:25:142;;;;14063:18;;14056:34;;;;14106:18;;;14099:34;4284:13:42;14149:18:142;;;14142:34;4307:4:42;14192:19:142;;;14185:61;13994:19;;4221:92:42;;;;;;;;;;;;4211:103;;;;;;4204:110;;4130:191;:::o;5140:1530:65:-;5266:7;;;6199:66;6186:79;;6182:164;;;-1:-1:-1;6297:1:65;;-1:-1:-1;6301:30:65;;-1:-1:-1;6333:1:65;6281:54;;6182:164;6457:24;;;6440:14;6457:24;;;;;;;;;14484:25:142;;;14557:4;14545:17;;14525:18;;;14518:45;;;;14579:18;;;14572:34;;;14622:18;;;14615:34;;;6457:24:65;;14456:19:142;;6457:24:65;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;6457:24:65;;-1:-1:-1;;6457:24:65;;;-1:-1:-1;;;;;;;6495:20:65;;6491:113;;-1:-1:-1;6547:1:65;;-1:-1:-1;6551:29:65;;-1:-1:-1;6547:1:65;;-1:-1:-1;6531:62:65;;6491:113;6622:6;-1:-1:-1;6630:20:65;;-1:-1:-1;6630:20:65;;-1:-1:-1;5140:1530:65;;;;;;;;;:::o;7196:532::-;7291:20;7282:5;:29;;;;;;;;:::i;:::-;;7278:444;;7196:532;;:::o;7278:444::-;7387:29;7378:5;:38;;;;;;;;:::i;:::-;;7374:348;;7439:23;;-1:-1:-1;;;7439:23:65;;;;;;;;;;;7374:348;7492:35;7483:5;:44;;;;;;;;:::i;:::-;;7479:243;;7550:46;;-1:-1:-1;;;7550:46:65;;;;;3796:25:142;;;3769:18;;7550:46:65;3650:177:142;7479:243:65;7626:30;7617:5;:39;;;;;;;;:::i;:::-;;7613:109;;7679:32;;-1:-1:-1;;;7679:32:65;;;;;3796:25:142;;;3769:18;;7679:32:65;3650:177:142;4059:629:57;4478:23;4504:33;-1:-1:-1;;;;;4504:27:57;;4532:4;4504:27;:33::i;:::-;4478:59;;4551:10;:17;4572:1;4551:22;;:57;;;;;4589:10;4578:30;;;;;;;;;;;;:::i;:::-;4577:31;4551:57;4547:135;;;4631:40;;-1:-1:-1;;;4631:40:57;;-1:-1:-1;;;;;9226:32:142;;4631:40:57;;;9208:51:142;9181:18;;4631:40:57;9062:203:142;7483:1170:36;-1:-1:-1;;;;;;;;;;;;;;;;7625:18:36;;7621:546;;7779:5;7761:1;:14;;;:23;;;;;;;:::i;:::-;;;;-1:-1:-1;7621:546:36;;-1:-1:-1;7621:546:36;;-1:-1:-1;;;;;7837:17:36;;7815:19;7837:17;;;;;;;;;;;7872:19;;;7868:115;;;7918:50;;-1:-1:-1;;;7918:50:36;;-1:-1:-1;;;;;10951:32:142;;7918:50:36;;;10933:51:142;11000:18;;;10993:34;;;11043:18;;;11036:34;;;10906:18;;7918:50:36;10731:345:142;7868:115:36;-1:-1:-1;;;;;8103:17:36;;:11;:17;;;;;;;;;;8123:19;;;;8103:39;;7621:546;-1:-1:-1;;;;;8181:16:36;;8177:429;;8344:14;;;:23;;;;;;;8177:429;;;-1:-1:-1;;;;;8557:15:36;;:11;:15;;;;;;;;;;:24;;;;;;8177:429;8636:2;-1:-1:-1;;;;;8621:25:36;8630:4;-1:-1:-1;;;;;8621:25:36;;8640:5;8621:25;;;;3796::142;;3784:2;3769:18;;3650:177;8621:25:36;;;;;;;;7558:1095;7483:1170;;;:::o;7058:687:42:-;7108:7;-1:-1:-1;;;;;;;;;;;7108:7:42;7203:13;:11;:13::i;:::-;7230:18;;7182:34;;-1:-1:-1;7230:22:42;7226:513;;7275:22;;;;;;;;7058:687;-1:-1:-1;;7058:687:42:o;7226:513::-;7572:13;;7603:15;;7599:130;;7645:10;7058:687;-1:-1:-1;;;7058:687:42:o;7599:130::-;7701:13;7694:20;;;;;7058:687;:::o;7966:723::-;8019:7;-1:-1:-1;;;;;;;;;;;8019:7:42;8117:16;:14;:16::i;:::-;8147:21;;8093:40;;-1:-1:-1;8147:25:42;8143:540;;8195:25;;;;;;;;7966:723;-1:-1:-1;;7966:723:42:o;8143:540::-;8507:16;;;;8541:18;;8537:136;;8586:13;7966:723;-1:-1:-1;;;7966:723:42:o;2705:151:61:-;2780:12;2811:38;2833:6;2841:4;2847:1;2780:12;3421;3435:23;3462:6;-1:-1:-1;;;;;3462:11:61;3481:5;3488:4;3462:31;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3420:73;;;;3510:55;3537:6;3545:7;3554:10;3510:26;:55::i;:::-;3503:62;3180:392;-1:-1:-1;;;;;;3180:392:61:o;4625:582::-;4769:12;4798:7;4793:408;;4821:19;4829:10;4821:7;:19::i;:::-;4793:408;;;5045:17;;:22;:49;;;;-1:-1:-1;;;;;;5071:18:61;;;:23;5045:49;5041:119;;;5121:24;;-1:-1:-1;;;5121:24:61;;-1:-1:-1;;;;;9226:32:142;;5121:24:61;;;9208:51:142;9181:18;;5121:24:61;9062:203:142;5041:119:61;-1:-1:-1;5180:10:61;5173:17;;5743:516;5874:17;;:21;5870:383;;6102:10;6096:17;6158:15;6145:10;6141:2;6137:19;6130:44;5870:383;6225:17;;-1:-1:-1;;;6225:17:61;;;;;;;;;;;14:286:142;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:142;;209:43;;199:71;;266:1;263;256:12;497:250;582:1;592:113;606:6;603:1;600:13;592:113;;;682:11;;;676:18;663:11;;;656:39;628:2;621:10;592:113;;;-1:-1:-1;;739:1:142;721:16;;714:27;497:250::o;752:271::-;794:3;832:5;826:12;859:6;854:3;847:19;875:76;944:6;937:4;932:3;928:14;921:4;914:5;910:16;875:76;:::i;:::-;1005:2;984:15;-1:-1:-1;;980:29:142;971:39;;;;1012:4;967:50;;752:271;-1:-1:-1;;752:271:142:o;1028:220::-;1177:2;1166:9;1159:21;1140:4;1197:45;1238:2;1227:9;1223:18;1215:6;1197:45;:::i;1253:131::-;-1:-1:-1;;;;;1328:31:142;;1318:42;;1308:70;;1374:1;1371;1364:12;1389:367;1457:6;1465;1518:2;1506:9;1497:7;1493:23;1489:32;1486:52;;;1534:1;1531;1524:12;1486:52;1573:9;1560:23;1592:31;1617:5;1592:31;:::i;:::-;1642:5;1720:2;1705:18;;;;1692:32;;-1:-1:-1;;;1389:367:142:o;1761:247::-;1820:6;1873:2;1861:9;1852:7;1848:23;1844:32;1841:52;;;1889:1;1886;1879:12;1841:52;1928:9;1915:23;1947:31;1972:5;1947:31;:::i;2013:127::-;2074:10;2069:3;2065:20;2062:1;2055:31;2105:4;2102:1;2095:15;2129:4;2126:1;2119:15;2145:726;2188:5;2241:3;2234:4;2226:6;2222:17;2218:27;2208:55;;2259:1;2256;2249:12;2208:55;2299:6;2286:20;2329:18;2321:6;2318:30;2315:56;;;2351:18;;:::i;:::-;2400:2;2394:9;2492:2;2454:17;;-1:-1:-1;;2450:31:142;;;2483:2;2446:40;2442:54;2430:67;;2527:18;2512:34;;2548:22;;;2509:62;2506:88;;;2574:18;;:::i;:::-;2610:2;2603:22;2634;;;2675:19;;;2696:4;2671:30;2668:39;-1:-1:-1;2665:59:142;;;2720:1;2717;2710:12;2665:59;2784:6;2777:4;2769:6;2765:17;2758:4;2750:6;2746:17;2733:58;2839:1;2811:19;;;2832:4;2807:30;2800:41;;;;2815:6;2145:726;-1:-1:-1;;;2145:726:142:o;2876:156::-;2942:20;;3002:4;2991:16;;2981:27;;2971:55;;3022:1;3019;3012:12;2971:55;2876:156;;;:::o;3037:608::-;3132:6;3140;3148;3201:2;3189:9;3180:7;3176:23;3172:32;3169:52;;;3217:1;3214;3207:12;3169:52;3257:9;3244:23;3290:18;3282:6;3279:30;3276:50;;;3322:1;3319;3312:12;3276:50;3345;3387:7;3378:6;3367:9;3363:22;3345:50;:::i;:::-;3335:60;;;3448:2;3437:9;3433:18;3420:32;3477:18;3467:8;3464:32;3461:52;;;3509:1;3506;3499:12;3461:52;3532;3576:7;3565:8;3554:9;3550:24;3532:52;:::i;:::-;3522:62;;;3603:36;3635:2;3624:9;3620:18;3603:36;:::i;:::-;3593:46;;3037:608;;;;;:::o;3832:508::-;3909:6;3917;3925;3978:2;3966:9;3957:7;3953:23;3949:32;3946:52;;;3994:1;3991;3984:12;3946:52;4033:9;4020:23;4052:31;4077:5;4052:31;:::i;:::-;4102:5;-1:-1:-1;4159:2:142;4144:18;;4131:32;4172:33;4131:32;4172:33;:::i;:::-;3832:508;;4224:7;;-1:-1:-1;;;4304:2:142;4289:18;;;;4276:32;;3832:508::o;4345:226::-;4404:6;4457:2;4445:9;4436:7;4432:23;4428:32;4425:52;;;4473:1;4470;4463:12;4425:52;-1:-1:-1;4518:23:142;;4345:226;-1:-1:-1;4345:226:142:o;4758:367::-;4826:6;4834;4887:2;4875:9;4866:7;4862:23;4858:32;4855:52;;;4903:1;4900;4893:12;4855:52;4948:23;;;-1:-1:-1;5047:2:142;5032:18;;5019:32;5060:33;5019:32;5060:33;:::i;:::-;5112:7;5102:17;;;4758:367;;;;;:::o;5550:1238::-;5956:3;5951;5947:13;5939:6;5935:26;5924:9;5917:45;5998:3;5993:2;5982:9;5978:18;5971:31;5898:4;6025:46;6066:3;6055:9;6051:19;6043:6;6025:46;:::i;:::-;6119:9;6111:6;6107:22;6102:2;6091:9;6087:18;6080:50;6153:33;6179:6;6171;6153:33;:::i;:::-;6217:2;6202:18;;6195:34;;;-1:-1:-1;;;;;6266:32:142;;6260:3;6245:19;;6238:61;6286:3;6315:19;;6308:35;;;6380:22;;;6374:3;6359:19;;6352:51;6452:13;;6474:22;;;6524:2;6550:15;;;;-1:-1:-1;6512:15:142;;;;-1:-1:-1;6593:169:142;6607:6;6604:1;6601:13;6593:169;;;6668:13;;6656:26;;6711:2;6737:15;;;;6702:12;;;;6629:1;6622:9;6593:169;;;-1:-1:-1;6779:3:142;;5550:1238;-1:-1:-1;;;;;;;;;;;5550:1238:142:o;6793:942::-;6904:6;6912;6920;6928;6936;6944;6952;7005:3;6993:9;6984:7;6980:23;6976:33;6973:53;;;7022:1;7019;7012:12;6973:53;7061:9;7048:23;7080:31;7105:5;7080:31;:::i;:::-;7130:5;-1:-1:-1;7187:2:142;7172:18;;7159:32;7200:33;7159:32;7200:33;:::i;:::-;7252:7;-1:-1:-1;7332:2:142;7317:18;;7304:32;;-1:-1:-1;7435:2:142;7420:18;;7407:32;;-1:-1:-1;7484:37:142;7516:3;7501:19;;7484:37;:::i;:::-;6793:942;;;;-1:-1:-1;6793:942:142;;;;7474:47;7594:3;7579:19;;7566:33;;-1:-1:-1;7698:3:142;7683:19;;;7670:33;;6793:942;-1:-1:-1;;6793:942:142:o;8284:388::-;8352:6;8360;8413:2;8401:9;8392:7;8388:23;8384:32;8381:52;;;8429:1;8426;8419:12;8381:52;8468:9;8455:23;8487:31;8512:5;8487:31;:::i;:::-;8537:5;-1:-1:-1;8594:2:142;8579:18;;8566:32;8607:33;8566:32;8607:33;:::i;8677:380::-;8756:1;8752:12;;;;8799;;;8820:61;;8874:4;8866:6;8862:17;8852:27;;8820:61;8927:2;8919:6;8916:14;8896:18;8893:38;8890:161;;8973:10;8968:3;8964:20;8961:1;8954:31;9008:4;9005:1;8998:15;9036:4;9033:1;9026:15;8890:161;;8677:380;;;:::o;11765:518::-;11867:2;11862:3;11859:11;11856:421;;;11903:5;11900:1;11893:16;11947:4;11944:1;11934:18;12017:2;12005:10;12001:19;11998:1;11994:27;11988:4;11984:38;12053:4;12041:10;12038:20;12035:47;;;-1:-1:-1;12076:4:142;12035:47;12131:2;12126:3;12122:12;12119:1;12115:20;12109:4;12105:31;12095:41;;12186:81;12204:2;12197:5;12194:13;12186:81;;;12263:1;12249:16;;12230:1;12219:13;12186:81;;12459:1299;12585:3;12579:10;12612:18;12604:6;12601:30;12598:56;;;12634:18;;:::i;:::-;12663:97;12753:6;12713:38;12745:4;12739:11;12713:38;:::i;:::-;12707:4;12663:97;:::i;:::-;12809:4;12840:2;12829:14;;12857:1;12852:649;;;;13545:1;13562:6;13559:89;;;-1:-1:-1;13614:19:142;;;13608:26;13559:89;-1:-1:-1;;12416:1:142;12412:11;;;12408:24;12404:29;12394:40;12440:1;12436:11;;;12391:57;13661:81;;12822:930;;12852:649;11712:1;11705:14;;;11749:4;11736:18;;-1:-1:-1;;12888:20:142;;;13006:222;13020:7;13017:1;13014:14;13006:222;;;13102:19;;;13096:26;13081:42;;13209:4;13194:20;;;;13162:1;13150:14;;;;13036:12;13006:222;;;13010:3;13256:6;13247:7;13244:19;13241:201;;;13317:19;;;13311:26;-1:-1:-1;;13400:1:142;13396:14;;;13412:3;13392:24;13388:37;13384:42;13369:58;13354:74;;13241:201;-1:-1:-1;;;;13488:1:142;13472:14;;;13468:22;13455:36;;-1:-1:-1;12459:1299:142:o;14660:127::-;14721:10;14716:3;14712:20;14709:1;14702:31;14752:4;14749:1;14742:15;14776:4;14773:1;14766:15;14792:277;14859:6;14912:2;14900:9;14891:7;14887:23;14883:32;14880:52;;;14928:1;14925;14918:12;14880:52;14960:9;14954:16;15013:5;15006:13;14999:21;14992:5;14989:32;14979:60;;15035:1;15032;15025:12;15074:222;15139:9;;;15160:10;;;15157:133;;;15212:10;15207:3;15203:20;15200:1;15193:31;15247:4;15244:1;15237:15;15275:4;15272:1;15265:15;15301:287;15430:3;15468:6;15462:13;15484:66;15543:6;15538:3;15531:4;15523:6;15519:17;15484:66;:::i;:::-;15566:16;;;;;15301:287;-1:-1:-1;;15301:287:142:o", "linkReferences": {}}, "methodIdentifiers": {"BLACKLISTER_ROLE()": "f515e6f2", "BURNER_ROLE()": "282c51f3", "DEFAULT_ADMIN_ROLE()": "a217fddf", "DOMAIN_SEPARATOR()": "3644e515", "MINTER_ROLE()": "d5391393", "SUPPORT_ROLE()": "94b059ab", "addBlackList(address)": "0ecb93c0", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "blacklisted(address)": "dbac26e9", "burn(uint256)": "42966c68", "burnFrom(address,uint256)": "79cc6790", "decimals()": "313ce567", "eip712Domain()": "84b0196e", "erc20Rescue(address,address,uint256)": "db50cce9", "getRoleAdmin(bytes32)": "248a9ca3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "initialize(string,string,uint8)": "1624f6c6", "mint(uint256)": "a0712d68", "mintTo(address,uint256)": "449a52f8", "name()": "06fdde03", "nonces(address)": "7ecebe00", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "removeBlackList(address)": "e4997dc5", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportsInterface(bytes4)": "01ffc9a7", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AddressInsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"AlreadyBlacklisted\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Blacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"ERC2612ExpiredSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC2612InvalidSigner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedInnerCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"currentNonce\",\"type\":\"uint256\"}],\"name\":\"InvalidAccountNonce\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"NotBlacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"AddedBlacklist\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"EIP712DomainChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"RemovedBlacklist\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"BLACKLISTER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"BURNER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MINTER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SUPPORT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"addBlackList\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"blacklisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burnFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract ERC20PermitUpgradeable\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"destination\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"erc20Rescue\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"decimals_\",\"type\":\"uint8\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"mintTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeBlackList\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Amir M. Shirif\",\"details\":\"Blacklisting has been included to prevent this currency from being used for illicit or nefarious activities\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"AddressInsufficientBalance(address)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}],\"AlreadyBlacklisted(address)\":[{\"details\":\"reverts if the blacklisting of an already blacklisted address is attempted\"}],\"Blacklisted(address)\":[{\"details\":\"reverts when blacklist attempting to interact\"}],\"ECDSAInvalidSignature()\":[{\"details\":\"The signature derives the `address(0)`.\"}],\"ECDSAInvalidSignatureLength(uint256)\":[{\"details\":\"The signature has an invalid length.\"}],\"ECDSAInvalidSignatureS(bytes32)\":[{\"details\":\"The signature has an S value that is in the upper half order.\"}],\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC2612ExpiredSignature(uint256)\":[{\"details\":\"Permit deadline has expired.\"}],\"ERC2612InvalidSigner(address,address)\":[{\"details\":\"Mismatched signature.\"}],\"FailedInnerCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"InvalidAccountNonce(address,uint256)\":[{\"details\":\"The nonce used for an `account` is not the expected current nonce.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotBlacklisted(address)\":[{\"details\":\"reverts if the removal of a blacklisting of an address not blacklisted is attempted\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC20 token failed.\"}]},\"events\":{\"AddedBlacklist(address)\":{\"details\":\"emits when address is blacklisted\"},\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"EIP712DomainChanged()\":{\"details\":\"MAY be emitted to signal that the domain could have changed.\"},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"RemovedBlacklist(address)\":{\"details\":\"emits when address is removed from blacklist\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted signaling this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call, an admin role bearer except when using {AccessControl-_setupRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.\"},\"addBlackList(address)\":{\"details\":\"restricted to BLACKLISTER_ROLE\",\"params\":{\"user\":\"blacklisted address\"}},\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"blacklisted(address)\":{\"returns\":{\"_0\":\"bool representing blacklist status\"}},\"burn(uint256)\":{\"details\":\"Only accounts with BURNER_ROLE can call this function.\",\"params\":{\"value\":\"The amount of tokens to burn.\"}},\"burnFrom(address,uint256)\":{\"details\":\"Only accounts with BURNER_ROLE can call this function.\",\"params\":{\"account\":\"The account from which tokens will be burned.\",\"value\":\"The amount of tokens to burn.\"}},\"eip712Domain()\":{\"details\":\"See {IERC-5267}.\"},\"erc20Rescue(address,address,uint256)\":{\"details\":\"restricted to SUPPORT_ROLE\",\"params\":{\"amount\":\"is the amount being transferred\",\"destination\":\"address where funds are returned\",\"token\":\"currency stuck in contract\"}},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"initialize(string,string,uint8)\":{\"details\":\"this function is called with proxy deployment to update state datauses initializer modifier to only allow one initialization per proxy\",\"params\":{\"decimals_\":\"is an int representing the number of decimals for the token\",\"name_\":\"is a string representing the token name\",\"symbol_\":\"is a string representing the token symbol\"}},\"mint(uint256)\":{\"details\":\"Only accounts with MINTER_ROLE can call this function.\",\"params\":{\"value\":\"The amount of tokens to mint.\"}},\"mintTo(address,uint256)\":{\"details\":\"Only accounts with MINTER_ROLE can call this function.\",\"params\":{\"account\":\"The account to which tokens will be minted.\",\"value\":\"The amount of tokens to mint.\"}},\"name()\":{\"details\":\"Returns the name of the token.\"},\"nonces(address)\":{\"details\":\"Returns the current nonce for `owner`. This value must be included whenever a signature is generated for {permit}. Every successful call to {permit} increases ``owner``'s nonce by one. This prevents a signature from being used multiple times.\"},\"permit(address,address,uint256,uint256,uint8,bytes32,bytes32)\":{\"details\":\"Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above.\"},\"removeBlackList(address)\":{\"details\":\"restricted to BLACKLISTER_ROLE\",\"params\":{\"user\":\"blacklisted address\"}},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"stateVariables\":{\"DECIMALS_SLOT\":{\"details\":\"Storage slot with the address of the current implementation. This is the keccak-256 hash of \\\"eip1967.telcoin.Stablecoin.decimals\\\" subtracted by 1.\"}},\"title\":\"Stablecoin\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addBlackList(address)\":{\"notice\":\"updates blacklisted list to include user\"},\"blacklisted(address)\":{\"notice\":\"returns blacklsit status of address\"},\"burn(uint256)\":{\"notice\":\"Burns `value` tokens from the caller's account.\"},\"burnFrom(address,uint256)\":{\"notice\":\"Burns `value` tokens from a specified `account`, deducting from the caller's allowance.\"},\"decimals()\":{\"notice\":\"Returns the number of decimal places\"},\"erc20Rescue(address,address,uint256)\":{\"notice\":\"sends tokens accidently sent to contract\"},\"initialize(string,string,uint8)\":{\"notice\":\"initializes the contract\"},\"mint(uint256)\":{\"notice\":\"Mints `value` tokens to the caller's account.\"},\"mintTo(address,uint256)\":{\"notice\":\"Mints `value` tokens to a specified `account`.\"},\"removeBlackList(address)\":{\"notice\":\"updates blacklisted list to remove user\"}},\"notice\":\"A Telcoin ContractThis is an ERC20 standard coin with advanced capabilities to allow for minting and burning. This coin is pegged to a fiat currency and its value is intended to reflect the value of its native currency\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"node_modules/telcoin-contracts/contracts/stablecoin/Stablecoin.sol\":\"Stablecoin\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/\",\":@axelar-network/=node_modules/@axelar-network/\",\":@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/\",\":@openzeppelin/=node_modules/@openzeppelin/\",\":@uniswap/=node_modules/@uniswap/\",\":ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/\",\":forge-std/=node_modules/forge-std/src/\",\":recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/\",\":solady/=node_modules/solady/src/\",\":telcoin-contracts/=node_modules/telcoin-contracts/\"]},\"sources\":{\"node_modules/@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x6662ec4e5cefca03eeadd073e9469df8d2944bb2ee8ec8f7622c2c46aab5f225\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4d8544c6f8daa4d1bc215c6a72fe0acdb748664a105b0e5efc19295667521d45\",\"dweb:/ipfs/QmdGWqdnXT8S3RgCR6aV8XHZrsybieMQLLnug1NtpSjEXN\"]},\"node_modules/@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"keccak256\":\"0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609\",\"dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM\"]},\"node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\":{\"keccak256\":\"0x9a1766b1921bf91b3e61eb53c7a6e70725254befd4bdcbbcd3af40bd9f66856f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://29bf2fa41a172086a665c9738377b93655aa4b1ffda9fe839c8bdf646f185040\",\"dweb:/ipfs/QmeB21qDuo8WPQSrqXJbQmWHKsdeocGNSUWLhCwniVejrt\"]},\"node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20PermitUpgradeable.sol\":{\"keccak256\":\"0x8a97653aeba40e9f0c2e8df1a1379b29b927b6dc3534040c668e71ad9ae89d88\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6e529c294c9d634eb68a1e4aeb66eb8381de5a08ccd2c0bfeebd48a6b28fcff7\",\"dweb:/ipfs/QmWCezuxfZb68nM3Hs6XzQNNiW7VJsymU4sajy2DW1CKbp\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/NoncesUpgradeable.sol\":{\"keccak256\":\"0x778f4a1546a1c6c726ecc8e2348a2789690fb8f26e12bd9d89537669167b79a4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://851d3dfe724e918ff0a064b206e1ef46b27ab0df2aa2c8af976973a22ef59827\",\"dweb:/ipfs/Qmd4wb7zX8ueYhMVBy5PJjfsANK3Ra3pKPN7qQkNsdwGHn\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/cryptography/EIP712Upgradeable.sol\":{\"keccak256\":\"0x85462422a22578744581e012e9aa0a391958cb360288b0b63f29bf0431d70327\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2bc529e2b9b28da5d26da451058250d85afcaa3c5083ee273ac68fa6bf956b78\",\"dweb:/ipfs/Qmd3Aq59ztmoVmHigsaR4YjkXWKERVpjfQ4a2PHk7Ke6Rx\"]},\"node_modules/@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xdaba3f7c42c55b2896353f32bd27d4d5f8bae741b3b05d4c53f67abc4dc47ce8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1fa2e61141c602510bcd2cd936ed9561922ac8772a9b9c9a9db091a74e354a45\",\"dweb:/ipfs/QmcHQDDoEBwJmwUbzoVkytvJsBx3KVHYFFnDkvRGWh9Wmh\"]},\"node_modules/@openzeppelin/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0xb6b36edd6a2999fd243ff226d6cbf84bd71af2432bbd0dfe19392996a1d9cb41\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1fd2f35495652e57e3f99bc6c510bc5f7dd398a176ea2e72d8ed730aebc6ca26\",\"dweb:/ipfs/QmTQV6X4gkikTib49cho5iDX3JvSQbdsoEChoDwrk3CbbH\"]},\"node_modules/@openzeppelin/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0x92aa1df62dc3d33f1656d63bede0923e0df0b706ad4137c8b10b0a8fe549fd92\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c5c0f29195ad64cbe556da8e257dac8f05f78c53f90323c0d2accf8e6922d33a\",\"dweb:/ipfs/QmQ61TED8uaCZwcbh8KkgRSsCav7x7HbcGHwHts3U4DmUP\"]},\"node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f\",\"dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c\",\"dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850\",\"dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d\",\"dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi\"]},\"node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0\",\"dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3\"]},\"node_modules/@openzeppelin/contracts/utils/Address.sol\":{\"keccak256\":\"0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245\",\"dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y\"]},\"node_modules/@openzeppelin/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c\",\"dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR\"]},\"node_modules/@openzeppelin/contracts/utils/Strings.sol\":{\"keccak256\":\"0x55f102ea785d8399c0e58d1108e2d289506dde18abc6db1b7f68c1f9f9bc5792\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6e52e0a7765c943ef14e5bcf11e46e6139fa044be564881378349236bf2e3453\",\"dweb:/ipfs/QmZEeeXoFPW47amyP35gfzomF9DixqqTEPwzBakv6cZw6i\"]},\"node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0xeed0a08b0b091f528356cbc7245891a4c748682d4f6a18055e8e6ca77d12a6cf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba80ba06c8e6be852847e4c5f4492cef801feb6558ae09ed705ff2e04ea8b13c\",\"dweb:/ipfs/QmXRJDv3xHLVQCVXg1ZvR35QS9sij5y9NDWYzMfUfAdTHF\"]},\"node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0xba333517a3add42cd35fe877656fc3dfcc9de53baa4f3aabbd6d12a92e4ea435\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ceacff44c0fdc81e48e0e0b1db87a2076d3c1fb497341de077bf1da9f6b406c\",\"dweb:/ipfs/QmRUo1muMRAewxrKQ7TkXUtknyRoR57AyEkoPpiuZQ8FzX\"]},\"node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4296879f55019b23e135000eb36896057e7101fb7fb859c5ef690cf14643757b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://87b3541437c8c443ccd36795e56a338ed12855eec17f8da624511b8d1a7e14df\",\"dweb:/ipfs/QmeJQCtZrQjtJLr6u7ZHWeH3pBnjtLWzvRrKViAi7UZqxL\"]},\"node_modules/@openzeppelin/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x005ec64c6313f0555d59e278f9a7a5ab2db5bdc72a027f255a37c327af1ec02d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ece9f0b9c8daca08c76b6b5405a6446b6f73b3a15fab7ff56e296cbd4a2c875\",\"dweb:/ipfs/QmQyRpyPRL5SQuAgj6SHmbir3foX65FJjbVTTQrA2EFg6L\"]},\"node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0x5f7e4076e175393767754387c962926577f1660dd9b810187b9002407656be72\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7d533a1c97cd43a57cd9c465f7ee8dd0e39ae93a8fb8ff8e5303a356b081cdcc\",\"dweb:/ipfs/QmVBEei6aTnvYNZp2CHYVNKyZS4q1KkjANfY39WVXZXVoT\"]},\"node_modules/telcoin-contracts/contracts/stablecoin/Stablecoin.sol\":{\"keccak256\":\"0x4782fd54d946422bbb45ddf6a13f73d8f7cd4843946685462468ef761307757d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e1f817e0b88fcbb814ebd68ef31bf700635df39de812203d9ae8e2b28f96c266\",\"dweb:/ipfs/QmZAm6Srd1ej3hnZmJMeSuvqSMDHrxETq8oXcBBSCN1s9M\"]},\"node_modules/telcoin-contracts/contracts/util/abstract/Blacklist.sol\":{\"keccak256\":\"0xb8b4825ef9c29eb37cdaf5855e749f40e9b63d8ed746ca80a58585e70efca80f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f8d5e75f35e91f1c53159c41d00bd50c34e5e54fe15b37fa4756ebd576d6fa6c\",\"dweb:/ipfs/QmaUvEy7yT8hVsXPc3WhJey1zaB5y3n5dq9TeTuNugZm7D\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "AddressInsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "type": "error", "name": "AlreadyBlacklisted"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "Blacklisted"}, {"inputs": [], "type": "error", "name": "ECDSAInvalidSignature"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "ECDSAInvalidSignatureLength"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "type": "error", "name": "ECDSAInvalidSignatureS"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "type": "error", "name": "ERC2612ExpiredSignature"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC2612InvalidSigner"}, {"inputs": [], "type": "error", "name": "FailedInnerCall"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "type": "error", "name": "InvalidAccountNonce"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "type": "error", "name": "NotBlacklisted"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": false}], "type": "event", "name": "AddedBlacklist", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [], "type": "event", "name": "EIP712DomainChanged", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "user", "type": "address", "indexed": false}], "type": "event", "name": "RemovedBlacklist", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "BLACKLISTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "BURNER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SUPPORT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addBlackList"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "view", "type": "function", "name": "blacklisted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burnFrom"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}]}, {"inputs": [{"internalType": "contract ERC20PermitUpgradeable", "name": "token", "type": "address"}, {"internalType": "address", "name": "destination", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "erc20Rescue"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mintTo"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "removeBlackList"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}."}, "addBlackList(address)": {"details": "restricted to BLACKLISTER_ROLE", "params": {"user": "blacklisted address"}}, "allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "blacklisted(address)": {"returns": {"_0": "bool representing blacklist status"}}, "burn(uint256)": {"details": "Only accounts with BURNER_ROLE can call this function.", "params": {"value": "The amount of tokens to burn."}}, "burnFrom(address,uint256)": {"details": "Only accounts with BURNER_ROLE can call this function.", "params": {"account": "The account from which tokens will be burned.", "value": "The amount of tokens to burn."}}, "eip712Domain()": {"details": "See {IERC-5267}."}, "erc20Rescue(address,address,uint256)": {"details": "restricted to SUPPORT_ROLE", "params": {"amount": "is the amount being transferred", "destination": "address where funds are returned", "token": "currency stuck in contract"}}, "getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "initialize(string,string,uint8)": {"details": "this function is called with proxy deployment to update state datauses initializer modifier to only allow one initialization per proxy", "params": {"decimals_": "is an int representing the number of decimals for the token", "name_": "is a string representing the token name", "symbol_": "is a string representing the token symbol"}}, "mint(uint256)": {"details": "Only accounts with MINTER_ROLE can call this function.", "params": {"value": "The amount of tokens to mint."}}, "mintTo(address,uint256)": {"details": "Only accounts with MINTER_ROLE can call this function.", "params": {"account": "The account to which tokens will be minted.", "value": "The amount of tokens to mint."}}, "name()": {"details": "Returns the name of the token."}, "nonces(address)": {"details": "Returns the current nonce for `owner`. This value must be included whenever a signature is generated for {permit}. Every successful call to {permit} increases ``owner``'s nonce by one. This prevents a signature from being used multiple times."}, "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": {"details": "Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above."}, "removeBlackList(address)": {"details": "restricted to BLACKLISTER_ROLE", "params": {"user": "blacklisted address"}}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addBlackList(address)": {"notice": "updates blacklisted list to include user"}, "blacklisted(address)": {"notice": "returns blacklsit status of address"}, "burn(uint256)": {"notice": "<PERSON> `value` tokens from the caller's account."}, "burnFrom(address,uint256)": {"notice": "<PERSON> `value` tokens from a specified `account`, deducting from the caller's allowance."}, "decimals()": {"notice": "Returns the number of decimal places"}, "erc20Rescue(address,address,uint256)": {"notice": "sends tokens accidently sent to contract"}, "initialize(string,string,uint8)": {"notice": "initializes the contract"}, "mint(uint256)": {"notice": "Mints `value` tokens to the caller's account."}, "mintTo(address,uint256)": {"notice": "Mints `value` tokens to a specified `account`."}, "removeBlackList(address)": {"notice": "updates blacklisted list to remove user"}}, "version": 1}}, "settings": {"remappings": ["@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/", "@axelar-network/=node_modules/@axelar-network/", "@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/", "@openzeppelin/=node_modules/@openzeppelin/", "@uniswap/=node_modules/@uniswap/", "ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/", "forge-std/=node_modules/forge-std/src/", "recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/", "solady/=node_modules/solady/src/", "telcoin-contracts/=node_modules/telcoin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"node_modules/telcoin-contracts/contracts/stablecoin/Stablecoin.sol": "Stablecoin"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"node_modules/@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol": {"keccak256": "0x6662ec4e5cefca03eeadd073e9469df8d2944bb2ee8ec8f7622c2c46aab5f225", "urls": ["bzz-raw://4d8544c6f8daa4d1bc215c6a72fe0acdb748664a105b0e5efc19295667521d45", "dweb:/ipfs/QmdGWqdnXT8S3RgCR6aV8XHZrsybieMQLLnug1NtpSjEXN"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol": {"keccak256": "0x631188737069917d2f909d29ce62c4d48611d326686ba6683e26b72a23bfac0b", "urls": ["bzz-raw://7a61054ae84cd6c4d04c0c4450ba1d6de41e27e0a2c4f1bcdf58f796b401c609", "dweb:/ipfs/QmUvtdp7X1mRVyC3CsHrtPbgoqWaXHp3S1ZR24tpAQYJWM"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol": {"keccak256": "0x9a1766b1921bf91b3e61eb53c7a6e70725254befd4bdcbbcd3af40bd9f66856f", "urls": ["bzz-raw://29bf2fa41a172086a665c9738377b93655aa4b1ffda9fe839c8bdf646f185040", "dweb:/ipfs/QmeB21qDuo8WPQSrqXJbQmWHKsdeocGNSUWLhCwniVejrt"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20PermitUpgradeable.sol": {"keccak256": "0x8a97653aeba40e9f0c2e8df1a1379b29b927b6dc3534040c668e71ad9ae89d88", "urls": ["bzz-raw://6e529c294c9d634eb68a1e4aeb66eb8381de5a08ccd2c0bfeebd48a6b28fcff7", "dweb:/ipfs/QmWCezuxfZb68nM3Hs6XzQNNiW7VJsymU4sajy2DW1CKbp"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/NoncesUpgradeable.sol": {"keccak256": "0x778f4a1546a1c6c726ecc8e2348a2789690fb8f26e12bd9d89537669167b79a4", "urls": ["bzz-raw://851d3dfe724e918ff0a064b206e1ef46b27ab0df2aa2c8af976973a22ef59827", "dweb:/ipfs/Qmd4wb7zX8ueYhMVBy5PJjfsANK3Ra3pKPN7qQkNsdwGHn"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/cryptography/EIP712Upgradeable.sol": {"keccak256": "0x85462422a22578744581e012e9aa0a391958cb360288b0b63f29bf0431d70327", "urls": ["bzz-raw://2bc529e2b9b28da5d26da451058250d85afcaa3c5083ee273ac68fa6bf956b78", "dweb:/ipfs/Qmd3Aq59ztmoVmHigsaR4YjkXWKERVpjfQ4a2PHk7Ke6Rx"], "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xdaba3f7c42c55b2896353f32bd27d4d5f8bae741b3b05d4c53f67abc4dc47ce8", "urls": ["bzz-raw://1fa2e61141c602510bcd2cd936ed9561922ac8772a9b9c9a9db091a74e354a45", "dweb:/ipfs/QmcHQDDoEBwJmwUbzoVkytvJsBx3KVHYFFnDkvRGWh9Wmh"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/access/IAccessControl.sol": {"keccak256": "0xb6b36edd6a2999fd243ff226d6cbf84bd71af2432bbd0dfe19392996a1d9cb41", "urls": ["bzz-raw://1fd2f35495652e57e3f99bc6c510bc5f7dd398a176ea2e72d8ed730aebc6ca26", "dweb:/ipfs/QmTQV6X4gkikTib49cho5iDX3JvSQbdsoEChoDwrk3CbbH"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/IERC5267.sol": {"keccak256": "0x92aa1df62dc3d33f1656d63bede0923e0df0b706ad4137c8b10b0a8fe549fd92", "urls": ["bzz-raw://c5c0f29195ad64cbe556da8e257dac8f05f78c53f90323c0d2accf8e6922d33a", "dweb:/ipfs/QmQ61TED8uaCZwcbh8KkgRSsCav7x7HbcGHwHts3U4DmUP"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x60c65f701957fdd6faea1acb0bb45825791d473693ed9ecb34726fdfaa849dd7", "urls": ["bzz-raw://ea290300e0efc4d901244949dc4d877fd46e6c5e43dc2b26620e8efab3ab803f", "dweb:/ipfs/QmcLLJppxKeJWqHxE2CUkcfhuRTgHSn8J4kijcLa5MYhSt"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xc6a8ff0ea489379b61faa647490411b80102578440ab9d84e9a957cc12164e70", "urls": ["bzz-raw://0ea104e577e63faea3b69c415637e99e755dcbf64c5833d7140c35a714d6d90c", "dweb:/ipfs/Qmau6x4Ns9XdyynRCNNp3RhLqijJjFm7z5fyZazfYFGYdq"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xaa761817f6cd7892fcf158b3c776b34551cde36f48ff9703d53898bc45a94ea2", "urls": ["bzz-raw://0ad7c8d4d08938c8dfc43d75a148863fb324b80cf53e0a36f7e5a4ac29008850", "dweb:/ipfs/QmcrhfPgVNf5mkdhQvy1pMv51TFokD3Y4Wa5WZhFqVh8UV"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x6008dabfe393240d73d7dd7688033f72740d570aa422254d29a7dce8568f3aff", "urls": ["bzz-raw://f5196ec75139918c6c7bb4251b36395e668f1fa6d206beba7e7520e74913940d", "dweb:/ipfs/QmSyqjksXxmm2mCG6qRd1yuwLykypkSVBbnBnGqJRcuJMi"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x37bb49513c49c87c4642a891b13b63571bc87013dde806617aa1efb54605f386", "urls": ["bzz-raw://b3036b3a83b7c48f96641f2a9002b9f2dcb6a5958dd670894ada21ae8229b3d0", "dweb:/ipfs/QmUNfSBdoVtjhETaUJCYcaC7pTMgbhht926tJ2uXJbiVd3"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Address.sol": {"keccak256": "0xaf28a975a78550e45f65e559a3ad6a5ad43b9b8a37366999abd1b7084eb70721", "urls": ["bzz-raw://b7bd24e224f67f65bfadf85dc2929fa965456bb2415478bd0125471b5ce35245", "dweb:/ipfs/QmRaydGr8BTHs1kvaZfsNU69pKzUAGFrvABn1KiRSbE51y"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"keccak256": "0x32ba59b4b7299237c8ba56319110989d7978a039faf754793064e967e5894418", "urls": ["bzz-raw://1ae50c8b562427df610cc4540c9bf104acca7ef8e2dcae567ae7e52272281e9c", "dweb:/ipfs/QmTHiadFCSJUPpRjNegc5SahmeU8bAoY8i9Aq6tVscbcKR"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/Strings.sol": {"keccak256": "0x55f102ea785d8399c0e58d1108e2d289506dde18abc6db1b7f68c1f9f9bc5792", "urls": ["bzz-raw://6e52e0a7765c943ef14e5bcf11e46e6139fa044be564881378349236bf2e3453", "dweb:/ipfs/QmZEeeXoFPW47amyP35gfzomF9DixqqTEPwzBakv6cZw6i"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0xeed0a08b0b091f528356cbc7245891a4c748682d4f6a18055e8e6ca77d12a6cf", "urls": ["bzz-raw://ba80ba06c8e6be852847e4c5f4492cef801feb6558ae09ed705ff2e04ea8b13c", "dweb:/ipfs/QmXRJDv3xHLVQCVXg1ZvR35QS9sij5y9NDWYzMfUfAdTHF"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0xba333517a3add42cd35fe877656fc3dfcc9de53baa4f3aabbd6d12a92e4ea435", "urls": ["bzz-raw://2ceacff44c0fdc81e48e0e0b1db87a2076d3c1fb497341de077bf1da9f6b406c", "dweb:/ipfs/QmRUo1muMRAewxrKQ7TkXUtknyRoR57AyEkoPpiuZQ8FzX"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4296879f55019b23e135000eb36896057e7101fb7fb859c5ef690cf14643757b", "urls": ["bzz-raw://87b3541437c8c443ccd36795e56a338ed12855eec17f8da624511b8d1a7e14df", "dweb:/ipfs/QmeJQCtZrQjtJLr6u7ZHWeH3pBnjtLWzvRrKViAi7UZqxL"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"keccak256": "0x005ec64c6313f0555d59e278f9a7a5ab2db5bdc72a027f255a37c327af1ec02d", "urls": ["bzz-raw://4ece9f0b9c8daca08c76b6b5405a6446b6f73b3a15fab7ff56e296cbd4a2c875", "dweb:/ipfs/QmQyRpyPRL5SQuAgj6SHmbir3foX65FJjbVTTQrA2EFg6L"], "license": "MIT"}, "node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"keccak256": "0x5f7e4076e175393767754387c962926577f1660dd9b810187b9002407656be72", "urls": ["bzz-raw://7d533a1c97cd43a57cd9c465f7ee8dd0e39ae93a8fb8ff8e5303a356b081cdcc", "dweb:/ipfs/QmVBEei6aTnvYNZp2CHYVNKyZS4q1KkjANfY39WVXZXVoT"], "license": "MIT"}, "node_modules/telcoin-contracts/contracts/stablecoin/Stablecoin.sol": {"keccak256": "0x4782fd54d946422bbb45ddf6a13f73d8f7cd4843946685462468ef761307757d", "urls": ["bzz-raw://e1f817e0b88fcbb814ebd68ef31bf700635df39de812203d9ae8e2b28f96c266", "dweb:/ipfs/QmZAm6Srd1ej3hnZmJMeSuvqSMDHrxETq8oXcBBSCN1s9M"], "license": "MIT"}, "node_modules/telcoin-contracts/contracts/util/abstract/Blacklist.sol": {"keccak256": "0xb8b4825ef9c29eb37cdaf5855e749f40e9b63d8ed746ca80a58585e70efca80f", "urls": ["bzz-raw://f8d5e75f35e91f1c53159c41d00bd50c34e5e54fe15b37fa4756ebd576d6fa6c", "dweb:/ipfs/QmaUvEy7yT8hVsXPc3WhJey1zaB5y3n5dq9TeTuNugZm7D"], "license": "MIT"}}, "version": 1}, "id": 112}