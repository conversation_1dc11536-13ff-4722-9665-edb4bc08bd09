{"abi": [{"type": "constructor", "inputs": [{"name": "previousSignersRetention_", "type": "uint256", "internalType": "uint256"}, {"name": "domainSeparator_", "type": "bytes32", "internalType": "bytes32"}, {"name": "minimumRotationDelay_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "approveMessages", "inputs": [{"name": "messages", "type": "tuple[]", "internalType": "struct Message[]", "components": [{"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "messageId", "type": "string", "internalType": "string"}, {"name": "sourceAddress", "type": "string", "internalType": "string"}, {"name": "contractAddress", "type": "address", "internalType": "address"}, {"name": "payloadHash", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "proof", "type": "tuple", "internalType": "struct Proof", "components": [{"name": "signers", "type": "tuple", "internalType": "struct WeightedSigners", "components": [{"name": "signers", "type": "tuple[]", "internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "weight", "type": "uint128", "internalType": "uint128"}]}, {"name": "threshold", "type": "uint128", "internalType": "uint128"}, {"name": "nonce", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "signatures", "type": "bytes[]", "internalType": "bytes[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "callContract", "inputs": [{"name": "destinationChain", "type": "string", "internalType": "string"}, {"name": "destinationContractAddress", "type": "string", "internalType": "string"}, {"name": "payload", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "contractId", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "domainSeparator", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "epoch", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "epochBySignersHash", "inputs": [{"name": "signersHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "implementation", "inputs": [], "outputs": [{"name": "implementation_", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "isCommandExecuted", "inputs": [{"name": "commandId", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isContractCallApproved", "inputs": [{"name": "commandId", "type": "bytes32", "internalType": "bytes32"}, {"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "sourceAddress", "type": "string", "internalType": "string"}, {"name": "contractAddress", "type": "address", "internalType": "address"}, {"name": "payloadHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isMessageApproved", "inputs": [{"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "messageId", "type": "string", "internalType": "string"}, {"name": "sourceAddress", "type": "string", "internalType": "string"}, {"name": "contractAddress", "type": "address", "internalType": "address"}, {"name": "payloadHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isMessageExecuted", "inputs": [{"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "messageId", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lastRotationTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "messageHashToSign", "inputs": [{"name": "signersHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "dataHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "messageToCommandId", "inputs": [{"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "messageId", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "minimumRotationDelay", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "operator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "owner_", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pending<PERSON><PERSON>er", "inputs": [], "outputs": [{"name": "owner_", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "previousSignersRetention", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proposeOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rotateSigners", "inputs": [{"name": "newSigners", "type": "tuple", "internalType": "struct WeightedSigners", "components": [{"name": "signers", "type": "tuple[]", "internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "weight", "type": "uint128", "internalType": "uint128"}]}, {"name": "threshold", "type": "uint128", "internalType": "uint128"}, {"name": "nonce", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "proof", "type": "tuple", "internalType": "struct Proof", "components": [{"name": "signers", "type": "tuple", "internalType": "struct WeightedSigners", "components": [{"name": "signers", "type": "tuple[]", "internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "weight", "type": "uint128", "internalType": "uint128"}]}, {"name": "threshold", "type": "uint128", "internalType": "uint128"}, {"name": "nonce", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "signatures", "type": "bytes[]", "internalType": "bytes[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setup", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "signersHashByEpoch", "inputs": [{"name": "signer<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "timeSinceRotation", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOperatorship", "inputs": [{"name": "newOperator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgrade", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "newImplementationCodeHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "params", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "validateContractCall", "inputs": [{"name": "commandId", "type": "bytes32", "internalType": "bytes32"}, {"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "sourceAddress", "type": "string", "internalType": "string"}, {"name": "payloadHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "valid", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "validateMessage", "inputs": [{"name": "sourceChain", "type": "string", "internalType": "string"}, {"name": "messageId", "type": "string", "internalType": "string"}, {"name": "sourceAddress", "type": "string", "internalType": "string"}, {"name": "payloadHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "valid", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "validateProof", "inputs": [{"name": "dataHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "proof", "type": "tuple", "internalType": "struct Proof", "components": [{"name": "signers", "type": "tuple", "internalType": "struct WeightedSigners", "components": [{"name": "signers", "type": "tuple[]", "internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "weight", "type": "uint128", "internalType": "uint128"}]}, {"name": "threshold", "type": "uint128", "internalType": "uint128"}, {"name": "nonce", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "signatures", "type": "bytes[]", "internalType": "bytes[]"}]}], "outputs": [{"name": "isLatestSigners", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "ContractCall", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "destinationChain", "type": "string", "indexed": false, "internalType": "string"}, {"name": "destinationContractAddress", "type": "string", "indexed": false, "internalType": "string"}, {"name": "payloadHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "payload", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "MessageApproved", "inputs": [{"name": "commandId", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "sourceChain", "type": "string", "indexed": false, "internalType": "string"}, {"name": "messageId", "type": "string", "indexed": false, "internalType": "string"}, {"name": "sourceAddress", "type": "string", "indexed": false, "internalType": "string"}, {"name": "contractAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "payloadHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "MessageExecuted", "inputs": [{"name": "commandId", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "OperatorshipTransferred", "inputs": [{"name": "newOperator", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferStarted", "inputs": [{"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SignersRotated", "inputs": [{"name": "epoch", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "signersHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "signers", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "newImplementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "DuplicateSigners", "inputs": [{"name": "signersHash", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InsufficientRotationDelay", "inputs": [{"name": "minimumRotationDelay", "type": "uint256", "internalType": "uint256"}, {"name": "lastRotationTimestamp", "type": "uint256", "internalType": "uint256"}, {"name": "timeElapsed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidCodeHash", "inputs": []}, {"type": "error", "name": "InvalidImplementation", "inputs": []}, {"type": "error", "name": "InvalidMessages", "inputs": []}, {"type": "error", "name": "InvalidOwner", "inputs": []}, {"type": "error", "name": "InvalidOwnerAddress", "inputs": []}, {"type": "error", "name": "InvalidS", "inputs": []}, {"type": "error", "name": "InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidSignature", "inputs": []}, {"type": "error", "name": "InvalidSignatureLength", "inputs": []}, {"type": "error", "name": "InvalidSigners", "inputs": []}, {"type": "error", "name": "InvalidThreshold", "inputs": []}, {"type": "error", "name": "InvalidV", "inputs": []}, {"type": "error", "name": "InvalidWeights", "inputs": []}, {"type": "error", "name": "LowSignaturesWeight", "inputs": []}, {"type": "error", "name": "MalformedSignatures", "inputs": []}, {"type": "error", "name": "NotLatestSigners", "inputs": []}, {"type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": []}, {"type": "error", "name": "NotProxy", "inputs": []}, {"type": "error", "name": "RedundantSignaturesProvided", "inputs": [{"name": "required", "type": "uint256", "internalType": "uint256"}, {"name": "provided", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "SetupFailed", "inputs": []}], "bytecode": {"object": "0x610100604052348015610010575f80fd5b506040516127d33803806127d383398101604081905261002f916100f8565b608083905260a082905260c0819052600161004981610057565b50503060e052506101239050565b6001600160a01b03811661007e57604051633649397d60e21b815260040160405180910390fd5b6040516001600160a01b038216907f04dba622d284ed0014ee4b9a6a68386be1a4c08a4913ae272de89199cc686163905f90a27f02016836a56b71f0d02689e69e326f4f4c1b9057164ef592671cf0d37c8040c0555f7f9855384122b55936fbfb8ca5120e63c6537a1ac40caf6ae33502b3c5da8c87d155565b5f805f6060848603121561010a575f80fd5b5050815160208301516040909301519094929350919050565b60805160a05160c05160e0516126616101725f395f6108bd01525f81816104c201528181611433015261146501525f81816104fc0152610c7901525f818161036e0152610d4b01526126615ff3fe608060405234801561000f575f80fd5b50600436106101dc575f3560e01c80638da5cb5b11610109578063e30c39781161009e578063f2fde38b1161006e578063f2fde38b146104e4578063f698da25146104f7578063f6a5f9f51461051e578063fd721b4714610531575f80fd5b8063e30c397814610482578063eb215f2814610496578063f10bfaa1146104aa578063f2213d37146104bd575f80fd5b8063a3499c73116100d9578063a3499c73146103f1578063cba5be6a14610404578063d26ff21014610417578063d435b83e14610444575f80fd5b80638da5cb5b146103905780638f686dc4146103a4578063900cf0cf146103b75780639ded06df146103de575f80fd5b8063570ca7351161017f578063710bf3221161014f578063710bf3221461032857806379ba50971461033b5780638291286c146103435780638c212aa514610369575f80fd5b8063570ca735146102aa5780635c60da1b146102db5780635f6970c31461030257806364f1d85a14610315575f80fd5b80632fbceb4f116101ba5780632fbceb4f14610230578063377fff55146102515780634091c6d5146102595780634a6a42d814610297575f80fd5b8063116399e4146101e05780631c92115f146102085780631d92c0bf1461021d575b5f80fd5b6101f36101ee366004611772565b610544565b60405190151581526020015b60405180910390f35b61021b6102163660046117f9565b610556565b005b61021b61022b366004611a69565b6105c1565b61024361023e366004611ab6565b610659565b6040519081526020016101ff565b610243610691565b610243610267366004611b20565b5f9081527f457f3fc26bf430b020fe76358b1bfaba57e1657ace718da6437cda9934eabfeb602052604090205490565b61021b6102a5366004611b37565b6106b1565b5f805160206125b5833981519152546001600160a01b03165b6040516001600160a01b0390911681526020016101ff565b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc546102c3565b6101f3610310366004611b52565b610735565b61021b610323366004611bd1565b61074f565b61021b610336366004611b37565b610798565b61021b610840565b7fd10cce6f0e3f6dc9eb8280c32952dc6e4759d0519c550c55d0f8747d13a64bfd610243565b6102437f000000000000000000000000000000000000000000000000000000000000000081565b5f805160206125d5833981519152546102c3565b6101f36103b2366004611c66565b61088d565b7f457f3fc26bf430b020fe76358b1bfaba57e1657ace718da6437cda9934eabfe854610243565b61021b6103ec366004611d1e565b6108ba565b61021b6103ff366004611d5c565b61090d565b6101f3610412366004611ab6565b610b93565b6101f3610425366004611b20565b5f9081525f805160206125f58339815191526020526040902054151590565b610243610452366004611b20565b5f9081527f457f3fc26bf430b020fe76358b1bfaba57e1657ace718da6437cda9934eabfea602052604090205490565b5f80516020612635833981519152546102c3565b5f8051602061261583398151915254610243565b6101f36104b8366004611d9b565b610bca565b6102437f000000000000000000000000000000000000000000000000000000000000000081565b61021b6104f2366004611b37565b610bf5565b6102437f000000000000000000000000000000000000000000000000000000000000000081565b6101f361052c366004611e3f565b610c32565b61024361053f366004611ed3565b610c4e565b5f61054f8383610ccf565b9392505050565b8181604051610566929190611ef3565b6040518091039020336001600160a01b03167f30ae6cc78c27e651745bf2ad08a11de83910ac1e347a52f7ac898c0fbef94dae8888888888886040516105b196959493929190611f2a565b60405180910390a3505050505050565b5f6001836040516020016105d6929190612015565b6040516020818303038152906040528051906020012090505f6106035f805160206125b583398151915290565b546001600160a01b031633141590505f61061d8385610ccf565b905081801561062a575080155b15610648576040516341f7d76160e11b815260040160405180910390fd5b6106528583610dc4565b5050505050565b5f84848484604051602001610671949392919061203c565b604051602081830303815290604052805190602001209050949350505050565b5f80516020612615833981519152545f906106ac9042612079565b905090565b5f805160206125b58339815191525433906001600160a01b031681148015906106fa57505f805160206125d5833981519152546001600160a01b0316816001600160a01b031614155b15610728576040516313053d9360e21b81526001600160a01b03821660048201526024015b60405180910390fd5b61073182610ec2565b5050565b5f610744878787878787610f23565b979650505050505050565b5f808484604051602001610765939291906120cd565b6040516020818303038152906040528051906020012090506107878183610ccf565b506107928484610faa565b50505050565b336107ae5f805160206125d58339815191525490565b6001600160a01b0316146107d5576040516330cd747160e01b815260040160405180910390fd5b6001600160a01b0381166107fc57604051633649397d60e21b815260040160405180910390fd5b6040516001600160a01b038216907fd9be0e8e07417e00f2521db636cb53e316fd288f5051f16d2aa2bf0c3938a876905f90a25f8051602061263583398151915255565b5f6108565f805160206126358339815191525490565b90506001600160a01b0381163314610881576040516349e27cff60e01b815260040160405180910390fd5b61088a8161100a565b50565b5f8061089b8a8a8a8a610659565b90506108ac818b8b89898989611085565b9a9950505050505050505050565b307f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03160361090357604051635f886e9d60e11b815260040160405180910390fd5b61073182826110c0565b336109235f805160206125d58339815191525490565b6001600160a01b03161461094a576040516330cd747160e01b815260040160405180910390fd5b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc546001600160a01b0316638291286c6040518163ffffffff1660e01b8152600401602060405180830381865afa1580156109a7573d5f803e3d5ffd5b505050506040513d601f19601f820116820180604052508101906109cb91906121d2565b846001600160a01b0316638291286c6040518163ffffffff1660e01b8152600401602060405180830381865afa158015610a07573d5f803e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610a2b91906121d2565b14610a495760405163340aafcd60e11b815260040160405180910390fd5b836001600160a01b03163f8314610a73576040516323e13ec960e21b815260040160405180910390fd5b7f360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8490556040516001600160a01b038516907fbc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3b905f90a28015610792575f846001600160a01b0316639ded06df60e01b8484604051602401610af69291906121e9565b60408051601f198184030181529181526020820180516001600160e01b03166001600160e01b0319909416939093179092529051610b34919061221e565b5f60405180830381855af49150503d805f8114610b6c576040519150601f19603f3d011682016040523d82523d5f602084013e610b71565b606091505b5050905080610652576040516397905dfb60e01b815260040160405180910390fd5b5f60015f805160206125f58339815191525f610bb188888888610659565b81526020019081526020015f2054149050949350505050565b5f80610bd889898989610659565b9050610be8818a8a888888610f23565b9998505050505050505050565b33610c0b5f805160206125d58339815191525490565b6001600160a01b031614610881576040516330cd747160e01b815260040160405180910390fd5b5f610c4288888888888888611085565b98975050505050505050565b6040517f19457468657265756d205369676e6564204d6573736167653a0a39360000000060208201527f0000000000000000000000000000000000000000000000000000000000000000603c820152605c8101839052607c81018290525f90609c016040516020818303038152906040528051906020012090505b92915050565b5f7f457f3fc26bf430b020fe76358b1bfaba57e1657ace718da6437cda9934eabfe836610cfc8480612239565b90505f81604051602001610d10919061224d565b60408051601f1981840301815291815281516020928301205f818152600387019093529120548454808214965091925090811580610d7657507f0000000000000000000000000000000000000000000000000000000000000000610d748383612079565b115b15610d9457604051635e231fff60e01b815260040160405180910390fd5b5f610d9f848a610c4e565b9050610db88186610db360208c018c612328565b611121565b50505050505092915050565b7f457f3fc26bf430b020fe76358b1bfaba57e1657ace718da6437cda9934eabfe8610dee836112fb565b610df782611419565b5f83604051602001610e09919061236d565b60408051601f19818403018152919052805160208201208354919250905f90610e3390600161237f565b8085555f81815260028601602090815260408083208690558583526003880190915290205490915015610e7c57604051634311167960e01b81526004810183905260240161071f565b5f8281526003850160205260409081902082905551829082907fe7cf1d3405bd906f8500af030e1130f3affbe991be73471a0d3983fe3ca61ebc906105b1908790612392565b805f805160206125b583398151915280546001600160a01b0319166001600160a01b0392831617905560405190821681527f1e3390359ca00dde8f3438aa04eceb3ee726e628c7daf84d23cc9619fcefdcb79060200160405180910390a150565b5f80610f34888888888833896114cc565b5f8981525f805160206125f583398151915260205260409020548114925090508115610f9f575f8881525f805160206125f58339815191526020526040808220600190555189917fe7d1e1f435233f7a187624ac11afaf32ee0da368cef8a5625be394412f61925491a25b509695505050505050565b805f819003610fcc57604051634eac1fe560e11b815260040160405180910390fd5b5f5b8181101561079257611002848483818110610feb57610feb6123c4565b9050602002810190610ffd91906123d8565b61150d565b600101610fce565b6001600160a01b03811661103157604051633649397d60e21b815260040160405180910390fd5b6040516001600160a01b038216907f04dba622d284ed0014ee4b9a6a68386be1a4c08a4913ae272de89199cc686163905f90a25f805160206125d5833981519152555f5f8051602061263583398151915255565b5f80611096898989898989896114cc565b5f998a525f805160206125f583398151915260205260409099205490981498975050505050505050565b5f806110ce838501856123ec565b90925090506001600160a01b038216156110eb576110eb82610ec2565b5f5b81518110156106525761111982828151811061110b5761110b6123c4565b60200260200101515f610dc4565b6001016110ed565b365f61112d85806124b0565b909250905080835f80805b838110156112e1575f6111a28c8b8b85818110611157576111576123c4565b905060200281019061116991906124f5565b8080601f0160208091040260200160405190810160405280939291908181526020018383808284375f9201919091525061162492505050565b90505b85841080156111ed57508787858181106111c1576111c16123c4565b6111d79260206040909202019081019150611b37565b6001600160a01b0316816001600160a01b031614155b15611202576111fb84612537565b93506111a5565b85840361122257604051630c37c53960e11b815260040160405180910390fd5b878785818110611234576112346123c4565b905060400201602001602081019061124c919061254f565b61125f906001600160801b03168461237f565b925061127160408c0160208d0161254f565b6001600160801b031683106112cd578461128c83600161237f565b0361129e575050505050505050610792565b6112a982600161237f565b6040516392b48b4f60e01b815260048101919091526024810186905260440161071f565b6112d684612537565b935050600101611138565b50604051630407644b60e31b815260040160405180910390fd5b805180515f81810361132057604051635e231fff60e01b815260040160405180910390fd5b5f805b838110156113cf575f85828151811061133e5761133e6123c4565b602002602001015190505f815f01519050806001600160a01b0316846001600160a01b03161061138157604051635e231fff60e01b815260040160405180910390fd5b8093505f82602001516001600160801b03169050805f036113b55760405163108cef9d60e31b815260040160405180910390fd5b6113bf818761237f565b9550505050806001019050611323565b5060208501516001600160801b03811615806113f35750806001600160801b031683105b156114115760405163aabd5a0960e01b815260040160405180910390fd5b505050505050565b5f80516020612615833981519152544282801561145e57507f000000000000000000000000000000000000000000000000000000000000000061145c8383612079565b105b156114b9577f00000000000000000000000000000000000000000000000000000000000000008261148f8184612079565b60405163767479ad60e01b815260048101939093526024830191909152604482015260640161071f565b5f80516020612615833981519152555050565b5f878787878787876040516020016114ea9796959493929190612568565b604051602081830303815290604052805190602001209050979650505050505050565b5f61152861151b83806124f5565b61023e60208601866124f5565b5f8181525f805160206125f583398151915260205260409020549091501561154e575050565b5f6115848261155d85806124f5565b61156a60408801886124f5565b61157a60808a0160608b01611b37565b89608001356114cc565b5f8381525f805160206125f5833981519152602052604090208190559050608083018035906115b69060608601611b37565b6001600160a01b0316837fcda53a2698efcca41b57faaddeb19bcb237bb1c71b479721fcc3a6fd41ba80976115eb87806124f5565b6115f860208a018a6124f5565b61160560408c018c6124f5565b60405161161796959493929190611f2a565b60405180910390a4505050565b5f815160411461164757604051634be6321b60e01b815260040160405180910390fd5b6020820151604083015160608401515f1a7f7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0821115611699576040516308183ce960e31b815260040160405180910390fd5b8060ff16601b141580156116b157508060ff16601c14155b156116cf5760405163119bce3960e01b815260040160405180910390fd5b604080515f81526020810180835288905260ff831691810191909152606081018490526080810183905260019060a0016020604051602081039080840390855afa15801561171f573d5f803e3d5ffd5b5050604051601f1901519450506001600160a01b03841661175357604051638baa579f60e01b815260040160405180910390fd5b50505092915050565b5f6040828403121561176c575f80fd5b50919050565b5f8060408385031215611783575f80fd5b8235915060208301356001600160401b0381111561179f575f80fd5b6117ab8582860161175c565b9150509250929050565b5f8083601f8401126117c5575f80fd5b5081356001600160401b038111156117db575f80fd5b6020830191508360208285010111156117f2575f80fd5b9250929050565b5f805f805f806060878903121561180e575f80fd5b86356001600160401b03811115611823575f80fd5b61182f89828a016117b5565b90975095505060208701356001600160401b0381111561184d575f80fd5b61185989828a016117b5565b90955093505060408701356001600160401b03811115611877575f80fd5b61188389828a016117b5565b979a9699509497509295939492505050565b634e487b7160e01b5f52604160045260245ffd5b604051606081016001600160401b03811182821017156118cb576118cb611895565b60405290565b604080519081016001600160401b03811182821017156118cb576118cb611895565b604051601f8201601f191681016001600160401b038111828210171561191b5761191b611895565b604052919050565b5f6001600160401b0382111561193b5761193b611895565b5060051b60200190565b6001600160a01b038116811461088a575f80fd5b80356001600160801b038116811461196f575f80fd5b919050565b5f60608284031215611984575f80fd5b61198c6118a9565b905081356001600160401b038111156119a3575f80fd5b8201601f810184136119b3575f80fd5b80356119c66119c182611923565b6118f3565b8082825260208201915060208360061b8501019250868311156119e7575f80fd5b6020840193505b82841015611a405760408488031215611a05575f80fd5b611a0d6118d1565b8435611a1881611945565b8152611a2660208601611959565b6020820152808352506020820191506040840193506119ee565b845250611a5291505060208301611959565b602082015260409182013591810191909152919050565b5f8060408385031215611a7a575f80fd5b82356001600160401b03811115611a8f575f80fd5b611a9b85828601611974565b92505060208301356001600160401b0381111561179f575f80fd5b5f805f8060408587031215611ac9575f80fd5b84356001600160401b03811115611ade575f80fd5b611aea878288016117b5565b90955093505060208501356001600160401b03811115611b08575f80fd5b611b14878288016117b5565b95989497509550505050565b5f60208284031215611b30575f80fd5b5035919050565b5f60208284031215611b47575f80fd5b813561054f81611945565b5f805f805f8060808789031215611b67575f80fd5b8635955060208701356001600160401b03811115611b83575f80fd5b611b8f89828a016117b5565b90965094505060408701356001600160401b03811115611bad575f80fd5b611bb989828a016117b5565b979a9699509497949695606090950135949350505050565b5f805f60408486031215611be3575f80fd5b83356001600160401b03811115611bf8575f80fd5b8401601f81018613611c08575f80fd5b80356001600160401b03811115611c1d575f80fd5b8660208260051b8401011115611c31575f80fd5b6020918201945092508401356001600160401b03811115611c50575f80fd5b611c5c8682870161175c565b9150509250925092565b5f805f805f805f8060a0898b031215611c7d575f80fd5b88356001600160401b03811115611c92575f80fd5b611c9e8b828c016117b5565b90995097505060208901356001600160401b03811115611cbc575f80fd5b611cc88b828c016117b5565b90975095505060408901356001600160401b03811115611ce6575f80fd5b611cf28b828c016117b5565b9095509350506060890135611d0681611945565b979a9699509497939692959194509192608001359150565b5f8060208385031215611d2f575f80fd5b82356001600160401b03811115611d44575f80fd5b611d50858286016117b5565b90969095509350505050565b5f805f8060608587031215611d6f575f80fd5b8435611d7a81611945565b93506020850135925060408501356001600160401b03811115611b08575f80fd5b5f805f805f805f6080888a031215611db1575f80fd5b87356001600160401b03811115611dc6575f80fd5b611dd28a828b016117b5565b90985096505060208801356001600160401b03811115611df0575f80fd5b611dfc8a828b016117b5565b90965094505060408801356001600160401b03811115611e1a575f80fd5b611e268a828b016117b5565b989b979a50959894979596606090950135949350505050565b5f805f805f805f60a0888a031215611e55575f80fd5b8735965060208801356001600160401b03811115611e71575f80fd5b611e7d8a828b016117b5565b90975095505060408801356001600160401b03811115611e9b575f80fd5b611ea78a828b016117b5565b9095509350506060880135611ebb81611945565b96999598509396929591949193505060809091013590565b5f8060408385031215611ee4575f80fd5b50508035926020909101359150565b818382375f9101908152919050565b81835281816020850137505f828201602090810191909152601f909101601f19169091010190565b606081525f611f3d60608301888a611f02565b8281036020840152611f50818789611f02565b90508281036040840152610be8818587611f02565b60028110611f8157634e487b7160e01b5f52602160045260245ffd5b9052565b8051606080845281519084018190525f9160200190829060808601905b80831015611fe257835180516001600160a01b031683526020908101516001600160801b0316818401529093019260019290920191604090910190611fa2565b5060208501519250611fff60208701846001600160801b03169052565b6040850151604087015280935050505092915050565b61201f8184611f65565b604060208201525f6120346040830184611f85565b949350505050565b838582375f848201605f60f81b8152838560018301375f93016001019283525090949350505050565b634e487b7160e01b5f52601160045260245ffd5b81810381811115610cc957610cc9612065565b5f808335601e198436030181126120a1575f80fd5b83016020810192503590506001600160401b038111156120bf575f80fd5b8036038213156117f2575f80fd5b5f604082016120dc8387611f65565b604060208401528390526060600584901b83018101908301855f609e1936839003015b878210156121c457868503605f19018452823581811261211d575f80fd5b8901612129818061208c565b60a0885261213b60a089018284611f02565b91505061214b602083018361208c565b88830360208a015261215e838284611f02565b9250505061216f604083018361208c565b88830360408a0152612182838284611f02565b92505050606082013561219481611945565b6001600160a01b0316606088015260809182013591909601526020938401939290920191600191909101906120ff565b509298975050505050505050565b5f602082840312156121e2575f80fd5b5051919050565b602081525f612034602083018486611f02565b5f5b838110156122165781810151838201526020016121fe565b50505f910152565b5f825161222f8184602087016121fc565b9190910192915050565b5f8235605e1983360301811261222f575f80fd5b602081525f608082018335601e19853603018112612269575f80fd5b84016020810190356001600160401b03811115612284575f80fd5b8060061b3603821315612295575f80fd5b6060602086015291829052905f9060a085015b818310156122f35783356122bb81611945565b6001600160a01b031681526001600160801b036122da60208601611959565b16602082015260409384019360019390930192016122a8565b6122ff60208801611959565b6001600160801b0316604087810191909152969096013560609095019490945250929392505050565b5f808335601e1984360301811261233d575f80fd5b8301803591506001600160401b03821115612356575f80fd5b6020019150600581901b36038213156117f2575f80fd5b602081525f61054f6020830184611f85565b80820180821115610cc957610cc9612065565b602081525f82518060208401526123b08160408501602087016121fc565b601f01601f19169190910160400192915050565b634e487b7160e01b5f52603260045260245ffd5b5f8235609e1983360301811261222f575f80fd5b5f80604083850312156123fd575f80fd5b823561240881611945565b915060208301356001600160401b03811115612422575f80fd5b8301601f81018513612432575f80fd5b80356124406119c182611923565b8082825260208201915060208360051b850101925087831115612461575f80fd5b602084015b838110156124a15780356001600160401b03811115612483575f80fd5b6124928a602083890101611974565b84525060209283019201612466565b50809450505050509250929050565b5f808335601e198436030181126124c5575f80fd5b8301803591506001600160401b038211156124de575f80fd5b6020019150600681901b36038213156117f2575f80fd5b5f808335601e1984360301811261250a575f80fd5b8301803591506001600160401b03821115612523575f80fd5b6020019150368190038213156117f2575f80fd5b5f6001820161254857612548612065565b5060010190565b5f6020828403121561255f575f80fd5b61054f82611959565b87815260a060208201525f61258160a08301888a611f02565b8281036040840152612594818789611f02565b6001600160a01b039590951660608401525050608001529594505050505056feca458dc12368669a3b8c292bc21c1b887ab1aa386fa3fcc1ed972afd74a330ca02016836a56b71f0d02689e69e326f4f4c1b9057164ef592671cf0d37c8040c0978b1ab9e384397ce0aab28eec0e3c25603b3210984045ad0e0f0a50d88cfc55457f3fc26bf430b020fe76358b1bfaba57e1657ace718da6437cda9934eabfe99855384122b55936fbfb8ca5120e63c6537a1ac40caf6ae33502b3c5da8c87d1a164736f6c634300081a000a", "sourceMap": "847:5853:27:-:0;;;1620:225;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1760:52:30;;;;1822:34;;;;1866:44;;;;1259:1:65;1208:26:68;1259:1:65;1208:18:68;:26::i;:::-;-1:-1:-1;;618:4:63;586:37;;-1:-1:-1;847:5853:27;;-1:-1:-1;847:5853:27;3490:302:68;-1:-1:-1;;;;;3567:22:68;;3563:56;;3598:21;;-1:-1:-1;;;3598:21:68;;;;;;;;;;;3563:56;3635:30;;-1:-1:-1;;;;;3635:30:68;;;;;;;;3706:11;3699:29;3774:1;3748:24;3741:35;3490:302::o;14:404:238:-;102:6;110;118;171:2;159:9;150:7;146:23;142:32;139:52;;;187:1;184;177:12;139:52;-1:-1:-1;;232:16:238;;312:2;297:18;;291:25;382:2;367:18;;;361:25;232:16;;291:25;;-1:-1:-1;361:25:238;14:404;-1:-1:-1;14:404:238:o;:::-;847:5853:27;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "847:5853:27:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5299:174;;;;;;:::i;:::-;;:::i;:::-;;;808:14:238;;801:22;783:41;;771:2;756:18;5299:174:27;;;;;;;;1786:284:29;;;;;;:::i;:::-;;:::i;:::-;;4434:573:27;;;;;;:::i;:::-;;:::i;4814:295:29:-;;;;;;:::i;:::-;;:::i;:::-;;;6578:25:238;;;6566:2;6551:18;4814:295:29;6432:177:238;3435:155:30;;;:::i;2808:167::-;;;;;;:::i;:::-;2880:7;2906:62;;;:49;:62;;;;;;;2808:167;5933:131:27;;;;;;:::i;:::-;;:::i;5599:117::-;-1:-1:-1;;;;;;;;;;;5668:41:27;-1:-1:-1;;;;;5668:41:27;5599:117;;;-1:-1:-1;;;;;7443:32:238;;;7425:51;;7413:2;7398:18;5599:117:27;7279:203:238;1421:167:65;1551:20;1545:27;1421:167;;5918:300:29;;;;;;:::i;:::-;;:::i;3627:260:27:-;;;;;;:::i;:::-;;:::i;2649:279:68:-;;;;;;:::i;:::-;;:::i;3052:192::-;;;:::i;3281:115:27:-;3352:37;3281:115;;956:49:30;;;;;1585:131:68;-1:-1:-1;;;;;;;;;;;1682:18:68;1585:131;;2667:427:29;;;;;;:::i;:::-;;:::i;2139:109:30:-;11914:27;2205:36;2139:109;;2993:126:65;;;;;;:::i;:::-;;:::i;1978:817::-;;;;;;:::i;:::-;;:::i;3452:239:29:-;;;;;;:::i;:::-;;:::i;5375:179::-;;;;;;:::i;:::-;5451:4;5474:50;;;-1:-1:-1;;;;;;;;;;;5474:50:29;;;;;;:73;;;5375:179;2444:167:30;;;;;;:::i;:::-;2516:7;2542:62;;;:49;:62;;;;;;;2444:167;1852:151:68;-1:-1:-1;;;;;;;;;;;1956:31:68;1852:151;;3128:141:30;-1:-1:-1;;;;;;;;;;;3210:52:30;3128:141;;4176:375:29;;;;;;:::i;:::-;;:::i;1172:45:30:-;;;;;2218:117:68;;;;;;:::i;:::-;;:::i;1067:40:30:-;;;;;5560:352:29;;;;;;:::i;:::-;;:::i;9989:269:30:-;;;;;;:::i;:::-;;:::i;5299:174:27:-;5385:20;5435:31;5450:8;5460:5;5435:14;:31::i;:::-;5417:49;5299:174;-1:-1:-1;;;5299:174:27:o;1786:284:29:-;2045:7;;2035:18;;;;;;;:::i;:::-;;;;;;;;1977:10;-1:-1:-1;;;;;1964:99:29;;1989:16;;2007:26;;2055:7;;1964:99;;;;;;;;;;;:::i;:::-;;;;;;;;1786:284;;;;;;:::o;4434:573:27:-;4533:16;4573:25;4600:10;4562:49;;;;;;;;;:::i;:::-;;;;;;;;;;;;;4552:60;;;;;;4533:79;;4623:25;4665:32;-1:-1:-1;;;;;;;;;;;6653:29:27;6499:199;4665:32;:41;-1:-1:-1;;;;;4665:41:27;4651:10;:55;;;-1:-1:-1;4665:41:27;4739:31;4754:8;4764:5;4739:14;:31::i;:::-;4716:54;;4784:20;:40;;;;;4809:15;4808:16;4784:40;4780:96;;;4847:18;;-1:-1:-1;;;4847:18:27;;;;;;;;;;;4780:96;4952:48;4967:10;4979:20;4952:14;:48::i;:::-;4523:484;;;4434:573;;:::o;4814:295:29:-;4919:7;5072:11;;5090:9;;5058:42;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;5042:60;;;;;;5035:67;;4814:295;;;;;;:::o;3435:155:30:-;-1:-1:-1;;;;;;;;;;;3531:52:30;3487:7;;3513:70;;:15;:70;:::i;:::-;3506:77;;3435:155;:::o;5933:131:27:-;-1:-1:-1;;;;;;;;;;;1943:41:27;1909:10;;-1:-1:-1;;;;;1943:41:27;1933:51;;;;;:72;;-1:-1:-1;;;;;;;;;;;;1682:18:68;-1:-1:-1;;;;;1988:17:27;:6;-1:-1:-1;;;;;1988:17:27;;;1933:72;1929:106;;;2014:21;;-1:-1:-1;;;2014:21:27;;-1:-1:-1;;;;;7443:32:238;;2014:21:27;;;7425:51:238;7398:18;;2014:21:27;;;;;;;;1929:106;6023:34:::1;6045:11;6023:21;:34::i;:::-;1882:172:::0;5933:131;:::o;5918:300:29:-;6113:10;6143:68;6160:9;6171:11;;6184:13;;6199:11;6143:16;:68::i;:::-;6135:76;5918:300;-1:-1:-1;;;;;;;5918:300:29:o;3627:260:27:-;3722:16;3762:27;3791:8;;3751:49;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;3741:60;;;;;;3722:79;;3812:31;3827:8;3837:5;3812:14;:31::i;:::-;;3854:26;3871:8;;3854:16;:26::i;:::-;3712:175;3627:260;;;:::o;2649:279:68:-;1401:10;1390:7;-1:-1:-1;;;;;;;;;;;1682:18:68;;1585:131;1390:7;-1:-1:-1;;;;;1390:21:68;;1386:44;;1420:10;;-1:-1:-1;;;1420:10:68;;;;;;;;;;;1386:44;-1:-1:-1;;;;;2734:22:68;::::1;2730:56;;2765:21;;-1:-1:-1::0;;;2765:21:68::1;;;;;;;;;;;2730:56;2802:34;::::0;-1:-1:-1;;;;;2802:34:68;::::1;::::0;::::1;::::0;;;::::1;-1:-1:-1::0;;;;;;;;;;;2870:42:68;2649:279::o;3052:192::-;3106:16;3125:14;-1:-1:-1;;;;;;;;;;;1956:31:68;;1852:151;3125:14;3106:33;-1:-1:-1;;;;;;3153:22:68;;3165:10;3153:22;3149:49;;3184:14;;-1:-1:-1;;;3184:14:68;;;;;;;;;;;3149:49;3209:28;3228:8;3209:18;:28::i;:::-;3096:148;3052:192::o;2667:427:29:-;2905:4;2921:17;2941:42;2960:11;;2973:9;;2941:18;:42::i;:::-;2921:62;;3000:87;3019:9;3030:11;;3043:13;;3058:15;3075:11;3000:18;:87::i;:::-;2993:94;2667:427;-1:-1:-1;;;;;;;;;;2667:427:29:o;2993:126:65:-;886:4:63;853:21;-1:-1:-1;;;;;853:38:63;;849:61;;900:10;;-1:-1:-1;;;900:10:63;;;;;;;;;;;849:61;3100:12:65::1;3107:4;;3100:6;:12::i;1978:817::-:0;1401:10:68;1390:7;-1:-1:-1;;;;;;;;;;;1682:18:68;;1585:131;1390:7;-1:-1:-1;;;;;1390:21:68;;1386:44;;1420:10;;-1:-1:-1;;;1420:10:68;;;;;;;;;;;1386:44;1551:20:65;1545:27;-1:-1:-1;;;;;2199:40:65::1;;:42;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2164:17;-1:-1:-1::0;;;;;2152:41:65::1;;:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:89;2148:137;;2262:23;;-1:-1:-1::0;;;2262:23:65::1;;;;;;;;;;;2148:137;2329:17;-1:-1:-1::0;;;;;2329:26:65::1;;2300:25;:55;2296:85;;2364:17;;-1:-1:-1::0;;;2364:17:65::1;;;;;;;;;;;2296:85;2422:20;2415:47:::0;;;2487:27:::1;::::0;-1:-1:-1;;;;;2487:27:65;::::1;::::0;::::1;::::0;;;::::1;2529:17:::0;;2525:264:::1;;2628:12;2646:17;-1:-1:-1::0;;;;;2646:30:65::1;2700:19;;;2721:6;;2677:51;;;;;;;;;:::i;:::-;;::::0;;-1:-1:-1;;2677:51:65;;::::1;::::0;;;;;;::::1;::::0;::::1;::::0;;-1:-1:-1;;;;;2677:51:65::1;-1:-1:-1::0;;;;;;2677:51:65;;::::1;::::0;;;::::1;::::0;;;2646:83;;::::1;::::0;2677:51;2646:83:::1;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2627:102;;;2749:7;2744:34;;2765:13;;-1:-1:-1::0;;;2765:13:65::1;;;;;;;;;;;3452:239:29::0;3558:4;997:1;-1:-1:-1;;;;;;;;;;;3581:39:29;3621:42;3640:11;;3653:9;;3621:18;:42::i;:::-;3581:83;;;;;;;;;;;;:103;3574:110;;3452:239;;;;;;:::o;4176:375::-;4374:10;4396:17;4416:42;4435:11;;4448:9;;4416:18;:42::i;:::-;4396:62;;4476:68;4493:9;4504:11;;4517:13;;4532:11;4476:16;:68::i;:::-;4468:76;4176:375;-1:-1:-1;;;;;;;;;4176:375:29:o;2218:117:68:-;1401:10;1390:7;-1:-1:-1;;;;;;;;;;;1682:18:68;;1585:131;1390:7;-1:-1:-1;;;;;1390:21:68;;1386:44;;1420:10;;-1:-1:-1;;;1420:10:68;;;;;;;;;;;5560:352:29;5795:4;5818:87;5837:9;5848:11;;5861:13;;5876:15;5893:11;5818:18;:87::i;:::-;5811:94;5560:352;-1:-1:-1;;;;;;;;5560:352:29:o;9989:269:30:-;10162:88;;22053:66:238;10162:88:30;;;22041:79:238;10211:15:30;22136:12:238;;;22129:28;22173:12;;;22166:28;;;22210:12;;;22203:28;;;10076:7:30;;22247:13:238;;10162:88:30;;;;;;;;;;;;10152:99;;;;;;10145:106;;9989:269;;;;;:::o;4388:744::-;4475:20;11914:27;4591:32;4626:13;:5;;:13;:::i;:::-;4591:48;;4650:19;4693:7;4682:19;;;;;;;;:::i;:::-;;;;-1:-1:-1;;4682:19:30;;;;;;;;;4672:30;;4682:19;4672:30;;;;4712:19;4734:36;;;:23;;;:36;;;;;;4803:10;;4842:27;;;;-1:-1:-1;4672:30:30;;-1:-1:-1;4734:36:30;4884:16;;;:73;;-1:-1:-1;4933:24:30;4904:26;4919:11;4904:12;:26;:::i;:::-;:53;4884:73;4880:102;;;4966:16;;-1:-1:-1;;;4966:16:30;;;;;;;;;;;4880:102;4993:19;5015:40;5033:11;5046:8;5015:17;:40::i;:::-;4993:62;-1:-1:-1;5066:59:30;4993:62;5099:7;5108:16;;;;:5;:16;:::i;:::-;5066:19;:59::i;:::-;4497:635;;;;;;4388:744;;;;:::o;5618:930::-;11914:27;5807:28;5824:10;5807:16;:28::i;:::-;5846:46;5871:20;5846:24;:46::i;:::-;5903:27;5944:10;5933:22;;;;;;;;:::i;:::-;;;;-1:-1:-1;;5933:22:30;;;;;;;;;5990:25;;5933:22;5990:25;;;6097:10;;5933:22;;-1:-1:-1;5990:25:30;5965:22;;6097:14;;6110:1;6097:14;:::i;:::-;6121:21;;;:10;6152:33;;;:23;;;:33;;;;;;;;:50;;;6323:39;;;:23;;;:39;;;;;;6078:33;;-1:-1:-1;6323:44:30;6319:89;;6376:32;;-1:-1:-1;;;6376:32:30;;;;;6578:25:238;;;6551:18;;6376:32:30;6432:177:238;6319:89:30;6419:39;;;;:23;;;:39;;;;;;;:50;;;6485:56;6443:14;;6461:8;;6485:56;;;;6526:14;;6485:56;:::i;6158:185:27:-;6273:11;-1:-1:-1;;;;;;;;;;;6229:55:27;;-1:-1:-1;;;;;;6229:55:27;-1:-1:-1;;;;;6229:55:27;;;;;;6300:36;;7443:32:238;;;7425:51;;6300:36:27;;7413:2:238;7398:18;6300:36:27;;;;;;;6158:185;:::o;7480:553:29:-;7662:10;7684:19;7706:76;7719:9;7730:11;;7743:13;;7758:10;7770:11;7706:12;:76::i;:::-;7800:39;:50;;;-1:-1:-1;;;;;;;;;;;7800:50:29;;;;;;:65;;;-1:-1:-1;7684:98:29;-1:-1:-1;7876:151:29;;;;7901:39;:50;;;-1:-1:-1;;;;;;;;;;;7901:50:29;;;;;;997:1;7901:69;;7990:26;7901:50;;7990:26;;;7876:151;7674:359;7480:553;;;;;;;;:::o;6440:330::-;6531:8;6514:14;6560:11;;;6556:41;;6580:17;;-1:-1:-1;;;6580:17:29;;;;;;;;;;;6556:41;6613:9;6608:156;6628:6;6624:1;:10;6608:156;;;6725:28;6741:8;;6750:1;6741:11;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;6725:15;:28::i;:::-;6636:3;;6608:156;;3490:302:68;-1:-1:-1;;;;;3567:22:68;;3563:56;;3598:21;;-1:-1:-1;;;3598:21:68;;;;;;;;;;;3563:56;3635:30;;-1:-1:-1;;;;;3635:30:68;;;;;;;;-1:-1:-1;;;;;;;;;;;3699:29:68;3774:1;-1:-1:-1;;;;;;;;;;;3741:35:68;3490:302::o;6864:430:29:-;7086:4;7102:19;7124:81;7137:9;7148:11;;7161:13;;7176:15;7193:11;7124:12;:81::i;:::-;7222:39;:50;;;-1:-1:-1;;;;;;;;;;;7222:50:29;;;;;;;:65;;;;;-1:-1:-1;;;;;;;;6864:430:29:o;2498:689:27:-;2564:17;;2619:46;;;;2630:4;2619:46;:::i;:::-;2563:102;;-1:-1:-1;2563:102:27;-1:-1:-1;;;;;;2986:23:27;;;2982:86;;3025:32;3047:9;3025:21;:32::i;:::-;3083:9;3078:103;3102:7;:14;3098:1;:18;3078:103;;;3137:33;3152:7;3160:1;3152:10;;;;;;;;:::i;:::-;;;;;;;3164:5;3137:14;:33::i;:::-;3118:3;;3078:103;;7763:1662:30;7938:33;;7974:23;:15;;:23;:::i;:::-;7938:59;;-1:-1:-1;7938:59:30;-1:-1:-1;7938:59:30;8082:10;8007:21;;;8363:976;8383:16;8379:1;:20;8363:976;;;8420:23;8446:41;8460:11;8473:10;;8484:1;8473:13;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;8446:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;8446:13:30;;-1:-1:-1;;;8446:41:30:i;:::-;8420:67;;8567:103;8588:13;8574:11;:27;:77;;;;;8624:7;;8632:11;8624:20;;;;;;;:::i;:::-;:27;;;:20;;;;;:27;;;;-1:-1:-1;8624:27:30;:::i;:::-;-1:-1:-1;;;;;8605:46:30;:15;-1:-1:-1;;;;;8605:46:30;;;8574:77;8567:103;;;8653:13;;;:::i;:::-;;;8567:103;;;8752:13;8737:11;:28;8733:62;;8774:21;;-1:-1:-1;;;8774:21:30;;;;;;;;;;;8733:62;8884:7;;8892:11;8884:20;;;;;;;:::i;:::-;;;;;;:27;;;;;;;;;;:::i;:::-;8870:41;;-1:-1:-1;;;;;8870:41:30;:11;:41;:::i;:::-;8856:55;-1:-1:-1;8992:25:30;;;;;;;;:::i;:::-;-1:-1:-1;;;;;8977:40:30;:11;:40;8973:269;;9125:16;9116:5;:1;9120;9116:5;:::i;:::-;:25;9112:38;;9143:7;;;;;;;;;;9112:38;9203:5;:1;9207;9203:5;:::i;:::-;9175:52;;-1:-1:-1;;;9175:52:30;;;;;28855:25:238;;;;28896:18;;;28889:34;;;28828:18;;9175:52:30;28681:248:238;8973:269:30;9315:13;;;:::i;:::-;;-1:-1:-1;;8401:3:30;;8363:976;;;;9397:21;;-1:-1:-1;;;9397:21:30;;;;;;;;;;;10532:1055;10656:23;;10706:14;;10622:31;10764:11;;;10760:40;;10784:16;;-1:-1:-1;;;10784:16:30;;;;;;;;;;;10760:40;10939:18;10986:9;10981:462;11005:6;11001:1;:10;10981:462;;;11032:36;11071:7;11079:1;11071:10;;;;;;;;:::i;:::-;;;;;;;11032:49;;11095:18;11116:14;:21;;;11095:42;;11170:10;-1:-1:-1;;;;;11156:24:30;:10;-1:-1:-1;;;;;11156:24:30;;11152:86;;11207:16;;-1:-1:-1;;;11207:16:30;;;;;;;;;;;11152:86;11265:10;11252:23;;11290:14;11307;:21;;;-1:-1:-1;;;;;11290:38:30;;;11347:6;11357:1;11347:11;11343:40;;11367:16;;-1:-1:-1;;;11367:16:30;;;;;;;;;;;11343:40;11412:20;11426:6;11412:11;:20;:::i;:::-;11398:34;;11018:425;;;11013:3;;;;;10981:462;;;-1:-1:-1;11473:25:30;;;;-1:-1:-1;;;;;11512:14:30;;;;:41;;;11544:9;-1:-1:-1;;;;;11530:23:30;:11;:23;11512:41;11508:72;;;11562:18;;-1:-1:-1;;;11562:18:30;;;;;;;;;;;11508:72;10612:975;;;;;10532:1055;:::o;6760:620::-;-1:-1:-1;;;;;;;;;;;6873:52:30;6962:15;6992:20;:90;;;;-1:-1:-1;7062:20:30;7017:41;7036:22;7017:16;:41;:::i;:::-;7016:66;6992:90;6988:304;;;7148:20;7186:22;7226:41;7186:22;7226:16;:41;:::i;:::-;7105:176;;-1:-1:-1;;;7105:176:30;;;;;29136:25:238;;;;29177:18;;;29170:34;;;;29220:18;;;29213:34;29109:18;;7105:176:30;28934:319:238;6988:304:30;-1:-1:-1;;;;;;;;;;;7302:71:30;-1:-1:-1;;6760:620:30:o;9401:339:29:-;9617:7;9664:9;9675:11;;9688:13;;9703:15;9720:11;9653:79;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;9643:90;;;;;;9636:97;;9401:339;;;;;;;;;:::o;8160:979::-;8347:17;8367:58;8386:19;:7;;:19;:::i;:::-;8407:17;;;;:7;:17;:::i;8367:58::-;929:1;8504:50;;;-1:-1:-1;;;;;;;;;;;8504:50:29;;;;;;8347:78;;-1:-1:-1;8504:73:29;8500:110;;8593:7;8160:979;:::o;8500:110::-;8620:19;8642:183;8668:9;8691:19;:7;;:19;:::i;:::-;8724:21;;;;:7;:21;:::i;:::-;8759:23;;;;;;;;:::i;:::-;8796:7;:19;;;8642:12;:183::i;:::-;8835:39;:50;;;-1:-1:-1;;;;;;;;;;;8835:50:29;;;;;:64;;;8620:205;-1:-1:-1;9103:19:29;;;;;;9066:23;;;;;;:::i;:::-;-1:-1:-1;;;;;8915:217:29;8944:9;8915:217;8967:19;:7;;:19;:::i;:::-;9000:17;;;;:7;:17;:::i;:::-;9031:21;;;;:7;:21;:::i;:::-;8915:217;;;;;;;;;;;:::i;:::-;;;;;;;;8220:919;;8160:979;:::o;1175:1902:55:-;1253:14;1321:9;:16;1341:2;1321:22;1317:59;;1352:24;;-1:-1:-1;;;1352:24:55;;;;;;;;;;;1317:59;1727:4;1712:20;;1706:27;1772:4;1757:20;;1751:27;1825:4;1810:20;;1804:27;1443:9;1796:36;2743:66;2730:79;;2726:102;;;2818:10;;-1:-1:-1;;;2818:10:55;;;;;;;;;;;2726:102;2843:1;:7;;2848:2;2843:7;;:18;;;;;2854:1;:7;;2859:2;2854:7;;2843:18;2839:41;;;2870:10;;-1:-1:-1;;;2870:10:55;;;;;;;;;;;2839:41;2900:24;;;;;;;;;;;;31328:25:238;;;31401:4;31389:17;;31369:18;;;31362:45;;;;31423:18;;;31416:34;;;31466:18;;;31459:34;;;2900:24:55;;31300:19:238;;2900:24:55;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2900:24:55;;-1:-1:-1;;2900:24:55;;;-1:-1:-1;;;;;;;3023:20:55;;3019:51;;3052:18;;-1:-1:-1;;;3052:18:55;;;;;;;;;;;3019:51;1269:1808;;;1175:1902;;;;:::o;14:153:238:-;72:5;117:2;108:6;103:3;99:16;95:25;92:45;;;133:1;130;123:12;92:45;-1:-1:-1;155:6:238;14:153;-1:-1:-1;14:153:238:o;172:466::-;265:6;273;326:2;314:9;305:7;301:23;297:32;294:52;;;342:1;339;332:12;294:52;387:23;;;-1:-1:-1;485:2:238;470:18;;457:32;-1:-1:-1;;;;;501:30:238;;498:50;;;544:1;541;534:12;498:50;567:65;624:7;615:6;604:9;600:22;567:65;:::i;:::-;557:75;;;172:466;;;;;:::o;835:348::-;887:8;897:6;951:3;944:4;936:6;932:17;928:27;918:55;;969:1;966;959:12;918:55;-1:-1:-1;992:20:238;;-1:-1:-1;;;;;1024:30:238;;1021:50;;;1067:1;1064;1057:12;1021:50;1104:4;1096:6;1092:17;1080:29;;1156:3;1149:4;1140:6;1132;1128:19;1124:30;1121:39;1118:59;;;1173:1;1170;1163:12;1118:59;835:348;;;;;:::o;1188:1020::-;1300:6;1308;1316;1324;1332;1340;1393:2;1381:9;1372:7;1368:23;1364:32;1361:52;;;1409:1;1406;1399:12;1361:52;1449:9;1436:23;-1:-1:-1;;;;;1474:6:238;1471:30;1468:50;;;1514:1;1511;1504:12;1468:50;1553:59;1604:7;1595:6;1584:9;1580:22;1553:59;:::i;:::-;1631:8;;-1:-1:-1;1527:85:238;-1:-1:-1;;1719:2:238;1704:18;;1691:32;-1:-1:-1;;;;;1735:32:238;;1732:52;;;1780:1;1777;1770:12;1732:52;1819:61;1872:7;1861:8;1850:9;1846:24;1819:61;:::i;:::-;1899:8;;-1:-1:-1;1793:87:238;-1:-1:-1;;1987:2:238;1972:18;;1959:32;-1:-1:-1;;;;;2003:32:238;;2000:52;;;2048:1;2045;2038:12;2000:52;2087:61;2140:7;2129:8;2118:9;2114:24;2087:61;:::i;:::-;1188:1020;;;;-1:-1:-1;1188:1020:238;;-1:-1:-1;1188:1020:238;;2167:8;;1188:1020;-1:-1:-1;;;1188:1020:238:o;2213:127::-;2274:10;2269:3;2265:20;2262:1;2255:31;2305:4;2302:1;2295:15;2329:4;2326:1;2319:15;2345:253;2417:2;2411:9;2459:4;2447:17;;-1:-1:-1;;;;;2479:34:238;;2515:22;;;2476:62;2473:88;;;2541:18;;:::i;:::-;2577:2;2570:22;2345:253;:::o;2603:257::-;2675:4;2669:11;;;2707:17;;-1:-1:-1;;;;;2739:34:238;;2775:22;;;2736:62;2733:88;;;2801:18;;:::i;2865:275::-;2936:2;2930:9;3001:2;2982:13;;-1:-1:-1;;2978:27:238;2966:40;;-1:-1:-1;;;;;3021:34:238;;3057:22;;;3018:62;3015:88;;;3083:18;;:::i;:::-;3119:2;3112:22;2865:275;;-1:-1:-1;2865:275:238:o;3145:197::-;3219:4;-1:-1:-1;;;;;3244:6:238;3241:30;3238:56;;;3274:18;;:::i;:::-;-1:-1:-1;3319:1:238;3315:14;3331:4;3311:25;;3145:197::o;3347:131::-;-1:-1:-1;;;;;3422:31:238;;3412:42;;3402:70;;3468:1;3465;3458:12;3483:188;3551:20;;-1:-1:-1;;;;;3600:46:238;;3590:57;;3580:85;;3661:1;3658;3651:12;3580:85;3483:188;;;:::o;3676:1418::-;3738:5;3786:4;3774:9;3769:3;3765:19;3761:30;3758:50;;;3804:1;3801;3794:12;3758:50;3826:22;;:::i;:::-;3817:31;;3884:9;3871:23;-1:-1:-1;;;;;3909:6:238;3906:30;3903:50;;;3949:1;3946;3939:12;3903:50;3972:22;;4025:4;4017:13;;4013:23;-1:-1:-1;4003:51:238;;4050:1;4047;4040:12;4003:51;4090:2;4077:16;4113:78;4129:61;4183:6;4129:61;:::i;:::-;4113:78;:::i;:::-;4213:3;4237:6;4232:3;4225:19;4269:4;4264:3;4260:14;4253:21;;4326:4;4316:6;4313:1;4309:14;4305:2;4301:23;4297:34;4283:48;;4354:3;4346:6;4343:15;4340:35;;;4371:1;4368;4361:12;4340:35;4403:4;4399:2;4395:13;4384:24;;4417:446;4433:6;4428:3;4425:15;4417:446;;;4511:4;4505:3;4500;4496:13;4492:24;4489:44;;;4529:1;4526;4519:12;4489:44;4561:22;;:::i;:::-;4624:3;4611:17;4641:33;4666:7;4641:33;:::i;:::-;4687:24;;4751:34;4779:4;4770:14;;4751:34;:::i;:::-;4744:4;4735:7;4731:18;4724:62;4811:7;4806:3;4799:20;;4848:4;4843:3;4839:14;4832:21;;4459:4;4454:3;4450:14;4443:21;;4417:446;;;4872:20;;-1:-1:-1;4926:40:238;;-1:-1:-1;;4960:4:238;4945:20;;4926:40;:::i;:::-;4919:4;4908:16;;4901:66;5040:4;5025:20;;;5012:34;5062:16;;;5055:33;;;;4912:5;3676:1418;-1:-1:-1;3676:1418:238:o;5099:607::-;5225:6;5233;5286:2;5274:9;5265:7;5261:23;5257:32;5254:52;;;5302:1;5299;5292:12;5254:52;5342:9;5329:23;-1:-1:-1;;;;;5367:6:238;5364:30;5361:50;;;5407:1;5404;5397:12;5361:50;5430:66;5488:7;5479:6;5468:9;5464:22;5430:66;:::i;:::-;5420:76;;;5549:2;5538:9;5534:18;5521:32;-1:-1:-1;;;;;5568:8:238;5565:32;5562:52;;;5610:1;5607;5600:12;5711:716;5803:6;5811;5819;5827;5880:2;5868:9;5859:7;5855:23;5851:32;5848:52;;;5896:1;5893;5886:12;5848:52;5936:9;5923:23;-1:-1:-1;;;;;5961:6:238;5958:30;5955:50;;;6001:1;5998;5991:12;5955:50;6040:59;6091:7;6082:6;6071:9;6067:22;6040:59;:::i;:::-;6118:8;;-1:-1:-1;6014:85:238;-1:-1:-1;;6206:2:238;6191:18;;6178:32;-1:-1:-1;;;;;6222:32:238;;6219:52;;;6267:1;6264;6257:12;6219:52;6306:61;6359:7;6348:8;6337:9;6333:24;6306:61;:::i;:::-;5711:716;;;;-1:-1:-1;6386:8:238;-1:-1:-1;;;;5711:716:238:o;6796:226::-;6855:6;6908:2;6896:9;6887:7;6883:23;6879:32;6876:52;;;6924:1;6921;6914:12;6876:52;-1:-1:-1;6969:23:238;;6796:226;-1:-1:-1;6796:226:238:o;7027:247::-;7086:6;7139:2;7127:9;7118:7;7114:23;7110:32;7107:52;;;7155:1;7152;7145:12;7107:52;7194:9;7181:23;7213:31;7238:5;7213:31;:::i;7487:951::-;7597:6;7605;7613;7621;7629;7637;7690:3;7678:9;7669:7;7665:23;7661:33;7658:53;;;7707:1;7704;7697:12;7658:53;7752:23;;;-1:-1:-1;7850:2:238;7835:18;;7822:32;-1:-1:-1;;;;;7866:30:238;;7863:50;;;7909:1;7906;7899:12;7863:50;7948:59;7999:7;7990:6;7979:9;7975:22;7948:59;:::i;:::-;8026:8;;-1:-1:-1;7922:85:238;-1:-1:-1;;8114:2:238;8099:18;;8086:32;-1:-1:-1;;;;;8130:32:238;;8127:52;;;8175:1;8172;8165:12;8127:52;8214:61;8267:7;8256:8;8245:9;8241:24;8214:61;:::i;:::-;7487:951;;;;-1:-1:-1;7487:951:238;;;;;8402:2;8387:18;;;8374:32;;7487:951;-1:-1:-1;;;;7487:951:238:o;8443:889::-;8590:6;8598;8606;8659:2;8647:9;8638:7;8634:23;8630:32;8627:52;;;8675:1;8672;8665:12;8627:52;8715:9;8702:23;-1:-1:-1;;;;;8740:6:238;8737:30;8734:50;;;8780:1;8777;8770:12;8734:50;8803:22;;8856:4;8848:13;;8844:27;-1:-1:-1;8834:55:238;;8885:1;8882;8875:12;8834:55;8925:2;8912:16;-1:-1:-1;;;;;8943:6:238;8940:30;8937:50;;;8983:1;8980;8973:12;8937:50;9038:7;9031:4;9021:6;9018:1;9014:14;9010:2;9006:23;9002:34;8999:47;8996:67;;;9059:1;9056;9049:12;8996:67;9090:4;9082:13;;;;-1:-1:-1;9114:6:238;-1:-1:-1;9158:20:238;;9145:34;-1:-1:-1;;;;;9191:32:238;;9188:52;;;9236:1;9233;9226:12;9188:52;9259:67;9318:7;9307:8;9296:9;9292:24;9259:67;:::i;:::-;9249:77;;;8443:889;;;;;:::o;9337:1278::-;9468:6;9476;9484;9492;9500;9508;9516;9524;9577:3;9565:9;9556:7;9552:23;9548:33;9545:53;;;9594:1;9591;9584:12;9545:53;9634:9;9621:23;-1:-1:-1;;;;;9659:6:238;9656:30;9653:50;;;9699:1;9696;9689:12;9653:50;9738:59;9789:7;9780:6;9769:9;9765:22;9738:59;:::i;:::-;9816:8;;-1:-1:-1;9712:85:238;-1:-1:-1;;9904:2:238;9889:18;;9876:32;-1:-1:-1;;;;;9920:32:238;;9917:52;;;9965:1;9962;9955:12;9917:52;10004:61;10057:7;10046:8;10035:9;10031:24;10004:61;:::i;:::-;10084:8;;-1:-1:-1;9978:87:238;-1:-1:-1;;10172:2:238;10157:18;;10144:32;-1:-1:-1;;;;;10188:32:238;;10185:52;;;10233:1;10230;10223:12;10185:52;10272:61;10325:7;10314:8;10303:9;10299:24;10272:61;:::i;:::-;10352:8;;-1:-1:-1;10246:87:238;-1:-1:-1;;10437:2:238;10422:18;;10409:32;10450:31;10409:32;10450:31;:::i;:::-;9337:1278;;;;-1:-1:-1;9337:1278:238;;;;;;;;-1:-1:-1;10500:5:238;;10578:3;10563:19;10550:33;;-1:-1:-1;9337:1278:238:o;10620:410::-;10690:6;10698;10751:2;10739:9;10730:7;10726:23;10722:32;10719:52;;;10767:1;10764;10757:12;10719:52;10807:9;10794:23;-1:-1:-1;;;;;10832:6:238;10829:30;10826:50;;;10872:1;10869;10862:12;10826:50;10911:59;10962:7;10953:6;10942:9;10938:22;10911:59;:::i;:::-;10989:8;;10885:85;;-1:-1:-1;10620:410:238;-1:-1:-1;;;;10620:410:238:o;11035:665::-;11123:6;11131;11139;11147;11200:2;11188:9;11179:7;11175:23;11171:32;11168:52;;;11216:1;11213;11206:12;11168:52;11255:9;11242:23;11274:31;11299:5;11274:31;:::i;:::-;11324:5;-1:-1:-1;11402:2:238;11387:18;;11374:32;;-1:-1:-1;11483:2:238;11468:18;;11455:32;-1:-1:-1;;;;;11499:30:238;;11496:50;;;11542:1;11539;11532:12;11890:1136;12012:6;12020;12028;12036;12044;12052;12060;12113:3;12101:9;12092:7;12088:23;12084:33;12081:53;;;12130:1;12127;12120:12;12081:53;12170:9;12157:23;-1:-1:-1;;;;;12195:6:238;12192:30;12189:50;;;12235:1;12232;12225:12;12189:50;12274:59;12325:7;12316:6;12305:9;12301:22;12274:59;:::i;:::-;12352:8;;-1:-1:-1;12248:85:238;-1:-1:-1;;12440:2:238;12425:18;;12412:32;-1:-1:-1;;;;;12456:32:238;;12453:52;;;12501:1;12498;12491:12;12453:52;12540:61;12593:7;12582:8;12571:9;12567:24;12540:61;:::i;:::-;12620:8;;-1:-1:-1;12514:87:238;-1:-1:-1;;12708:2:238;12693:18;;12680:32;-1:-1:-1;;;;;12724:32:238;;12721:52;;;12769:1;12766;12759:12;12721:52;12808:61;12861:7;12850:8;12839:9;12835:24;12808:61;:::i;:::-;11890:1136;;;;-1:-1:-1;11890:1136:238;;;;;;12992:2;12977:18;;;12964:32;;11890:1136;-1:-1:-1;;;;11890:1136:238:o;13031:1093::-;13150:6;13158;13166;13174;13182;13190;13198;13251:3;13239:9;13230:7;13226:23;13222:33;13219:53;;;13268:1;13265;13258:12;13219:53;13313:23;;;-1:-1:-1;13411:2:238;13396:18;;13383:32;-1:-1:-1;;;;;13427:30:238;;13424:50;;;13470:1;13467;13460:12;13424:50;13509:59;13560:7;13551:6;13540:9;13536:22;13509:59;:::i;:::-;13587:8;;-1:-1:-1;13483:85:238;-1:-1:-1;;13675:2:238;13660:18;;13647:32;-1:-1:-1;;;;;13691:32:238;;13688:52;;;13736:1;13733;13726:12;13688:52;13775:61;13828:7;13817:8;13806:9;13802:24;13775:61;:::i;:::-;13855:8;;-1:-1:-1;13749:87:238;-1:-1:-1;;13942:2:238;13927:18;;13914:32;13955:33;13914:32;13955:33;:::i;:::-;13031:1093;;;;-1:-1:-1;13031:1093:238;;;;;;;;-1:-1:-1;;14087:3:238;14072:19;;;14059:33;;13031:1093::o;14129:346::-;14197:6;14205;14258:2;14246:9;14237:7;14233:23;14229:32;14226:52;;;14274:1;14271;14264:12;14226:52;-1:-1:-1;;14319:23:238;;;14439:2;14424:18;;;14411:32;;-1:-1:-1;14129:346:238:o;14480:271::-;14663:6;14655;14650:3;14637:33;14619:3;14689:16;;14714:13;;;14689:16;14480:271;-1:-1:-1;14480:271:238:o;14756:267::-;14845:6;14840:3;14833:19;14897:6;14890:5;14883:4;14878:3;14874:14;14861:43;-1:-1:-1;14949:1:238;14924:16;;;14942:4;14920:27;;;14913:38;;;;15005:2;14984:15;;;-1:-1:-1;;14980:29:238;14971:39;;;14967:50;;14756:267::o;15028:625::-;15301:2;15290:9;15283:21;15264:4;15327:62;15385:2;15374:9;15370:18;15362:6;15354;15327:62;:::i;:::-;15437:9;15429:6;15425:22;15420:2;15409:9;15405:18;15398:50;15471;15514:6;15506;15498;15471:50;:::i;:::-;15457:64;;15569:9;15561:6;15557:22;15552:2;15541:9;15537:18;15530:50;15597;15640:6;15632;15624;15597:50;:::i;15658:239::-;15741:1;15734:5;15731:12;15721:143;;15786:10;15781:3;15777:20;15774:1;15767:31;15821:4;15818:1;15811:15;15849:4;15846:1;15839:15;15721:143;15873:18;;15658:239::o;16026:904::-;16157:12;;16123:4;16178:17;;;16244:19;;16114:14;;;16272:20;;;16084:3;;16364:4;16346:23;;16084:3;;16319;16310:13;;;16397:337;16411:6;16408:1;16405:13;16397:337;;;16470:13;;16514:9;;-1:-1:-1;;;;;16510:35:238;16496:50;;16602:4;16594:13;;;16588:20;-1:-1:-1;;;;;16584:61:238;16566:16;;;16559:87;16707:17;;;;16542:1;16426:9;;;;;16679:4;16668:16;;;;16397:337;;;16401:3;16782:4;16775:5;16771:16;16765:23;16743:45;;16797:50;16841:4;16836:3;16832:14;16816;-1:-1:-1;;;;;15968:46:238;15956:59;;15902:119;16797:50;16896:4;16889:5;16885:16;16879:23;16872:4;16867:3;16863:14;16856:47;16919:5;16912:12;;;;;16026:904;;;;:::o;16935:388::-;17154:46;17190:9;17182:6;17154:46;:::i;:::-;17236:2;17231;17220:9;17216:18;17209:30;17135:4;17256:61;17313:2;17302:9;17298:18;17290:6;17256:61;:::i;:::-;17248:69;16935:388;-1:-1:-1;;;;16935:388:238:o;17328:536::-;17661:6;17653;17648:3;17635:33;17617:3;17696:6;17691:3;17687:16;-1:-1:-1;;;17719:2:238;17712:15;17769:6;17761;17757:1;17753:2;17749:10;17736:40;17838:1;17799:15;;17816:1;17795:23;17827:13;;;-1:-1:-1;17795:23:238;;17328:536;-1:-1:-1;;;;17328:536:238:o;17869:127::-;17930:10;17925:3;17921:20;17918:1;17911:31;17961:4;17958:1;17951:15;17985:4;17982:1;17975:15;18001:128;18068:9;;;18089:11;;;18086:37;;;18103:18;;:::i;18134:501::-;18193:5;18200:6;18260:3;18247:17;18346:2;18342:7;18331:8;18315:14;18311:29;18307:43;18287:18;18283:68;18273:96;;18365:1;18362;18355:12;18273:96;18393:33;;18497:4;18484:18;;;-1:-1:-1;18445:21:238;;-1:-1:-1;;;;;;18514:30:238;;18511:50;;;18557:1;18554;18547:12;18511:50;18604:6;18588:14;18584:27;18577:5;18573:39;18570:59;;;18625:1;18622;18615:12;18640:2134;18886:4;18934:2;18923:9;18919:18;18946:46;18982:9;18974:6;18946:46;:::i;:::-;19028:2;19023;19008:18;;19001:30;19066:22;;;19119:2;19168:1;19164:14;;;19149:30;;19145:39;;;19104:18;;19207:6;19231:1;-1:-1:-1;;19259:14:238;19255:27;;;19251:42;19302:1443;19316:6;19313:1;19310:13;19302:1443;;;19381:22;;;-1:-1:-1;;19377:36:238;19365:49;;19453:20;;19496:27;;;19486:55;;19537:1;19534;19527:12;19486:55;19567:31;;19645:45;19567:31;;19645:45;:::i;:::-;19718:4;19710:6;19703:20;19750:73;19817:4;19809:6;19805:17;19791:12;19777;19750:73;:::i;:::-;19736:87;;;19874:54;19924:2;19917:5;19913:14;19906:5;19874:54;:::i;:::-;19977:6;19969;19965:19;19960:2;19952:6;19948:15;19941:44;20012:66;20071:6;20055:14;20039;20012:66;:::i;:::-;19998:80;;;;20129:54;20179:2;20172:5;20168:14;20161:5;20129:54;:::i;:::-;20232:6;20224;20220:19;20215:2;20207:6;20203:15;20196:44;20267:66;20326:6;20310:14;20294;20267:66;:::i;:::-;20253:80;;;;20385:2;20378:5;20374:14;20361:28;20402:33;20427:7;20402:33;:::i;:::-;-1:-1:-1;;;;;20472:33:238;20467:2;20455:15;;20448:58;20583:4;20572:16;;;20559:30;20609:17;;;;20602:34;20700:2;20723:12;;;;20688:15;;;;;20502:1;19331:9;;;;;19302:1443;;;-1:-1:-1;20762:6:238;;18640:2134;-1:-1:-1;;;;;;;;18640:2134:238:o;20779:184::-;20849:6;20902:2;20890:9;20881:7;20877:23;20873:32;20870:52;;;20918:1;20915;20908:12;20870:52;-1:-1:-1;20941:16:238;;20779:184;-1:-1:-1;20779:184:238:o;20968:245::-;21125:2;21114:9;21107:21;21088:4;21145:62;21203:2;21192:9;21188:18;21180:6;21172;21145:62;:::i;21218:250::-;21303:1;21313:113;21327:6;21324:1;21321:13;21313:113;;;21403:11;;;21397:18;21384:11;;;21377:39;21349:2;21342:10;21313:113;;;-1:-1:-1;;21460:1:238;21442:16;;21435:27;21218:250::o;21473:287::-;21602:3;21640:6;21634:13;21656:66;21715:6;21710:3;21703:4;21695:6;21691:17;21656:66;:::i;:::-;21738:16;;;;;21473:287;-1:-1:-1;;21473:287:238:o;22271:332::-;22372:4;22430:11;22417:25;22524:2;22520:7;22509:8;22493:14;22489:29;22485:43;22465:18;22461:68;22451:96;;22543:1;22540;22533:12;22608:1534;22805:2;22794:9;22787:21;22768:4;22846:3;22835:9;22831:19;22898:6;22885:20;22985:2;22981:7;22972:6;22956:14;22952:27;22948:41;22928:18;22924:66;22914:94;;23004:1;23001;22994:12;22914:94;23030:31;;23138:2;23127:14;;;23084:19;-1:-1:-1;;;;;23153:30:238;;23150:50;;;23196:1;23193;23186:12;23150:50;23252:6;23249:1;23245:14;23229;23225:35;23216:7;23212:49;23209:69;;;23274:1;23271;23264:12;23209:69;23314:4;23309:2;23294:18;;23287:32;23354:22;;;;23434:7;23459:1;;23407:3;23392:19;;23469:398;23483:6;23480:1;23477:13;23469:398;;;23560:6;23547:20;23580:33;23605:7;23580:33;:::i;:::-;-1:-1:-1;;;;;23638:33:238;23626:46;;-1:-1:-1;;;;;23710:35:238;23741:2;23729:15;;23710:35;:::i;:::-;23706:76;23701:2;23692:12;;23685:98;23812:4;23840:17;;;;23505:1;23498:9;;;;;23803:14;23469:398;;;23896:35;23927:2;23919:6;23915:15;23896:35;:::i;:::-;-1:-1:-1;;;;;15968:46:238;23988:4;23973:20;;;15956:59;;;;24052:17;;;;24039:31;24101:4;24086:20;;;24079:37;;;;-1:-1:-1;24052:17:238;;24133:3;-1:-1:-1;;;22608:1534:238:o;24147:556::-;24251:4;24257:6;24317:11;24304:25;24411:2;24407:7;24396:8;24380:14;24376:29;24372:43;24352:18;24348:68;24338:96;;24430:1;24427;24420:12;24338:96;24457:33;;24509:20;;;-1:-1:-1;;;;;;24541:30:238;;24538:50;;;24584:1;24581;24574:12;24538:50;24617:4;24605:17;;-1:-1:-1;24668:1:238;24664:14;;;24648;24644:35;24634:46;;24631:66;;;24693:1;24690;24683:12;24708:282;24903:2;24892:9;24885:21;24866:4;24923:61;24980:2;24969:9;24965:18;24957:6;24923:61;:::i;24995:125::-;25060:9;;;25081:10;;;25078:36;;;25094:18;;:::i;25125:394::-;25272:2;25261:9;25254:21;25235:4;25304:6;25298:13;25347:6;25342:2;25331:9;25327:18;25320:34;25363:79;25435:6;25430:2;25419:9;25415:18;25410:2;25402:6;25398:15;25363:79;:::i;:::-;25503:2;25482:15;-1:-1:-1;;25478:29:238;25463:45;;;;25510:2;25459:54;;25125:394;-1:-1:-1;;25125:394:238:o;25524:127::-;25585:10;25580:3;25576:20;25573:1;25566:31;25616:4;25613:1;25606:15;25640:4;25637:1;25630:15;25656:325;25749:4;25807:11;25794:25;25901:3;25897:8;25886;25870:14;25866:29;25862:44;25842:18;25838:69;25828:97;;25921:1;25918;25911:12;25986:1249;26120:6;26128;26181:2;26169:9;26160:7;26156:23;26152:32;26149:52;;;26197:1;26194;26187:12;26149:52;26236:9;26223:23;26255:31;26280:5;26255:31;:::i;:::-;26305:5;-1:-1:-1;26361:2:238;26346:18;;26333:32;-1:-1:-1;;;;;26377:30:238;;26374:50;;;26420:1;26417;26410:12;26374:50;26443:22;;26496:4;26488:13;;26484:27;-1:-1:-1;26474:55:238;;26525:1;26522;26515:12;26474:55;26565:2;26552:16;26588:78;26604:61;26658:6;26604:61;:::i;26588:78::-;26688:3;26712:6;26707:3;26700:19;26744:2;26739:3;26735:12;26728:19;;26799:2;26789:6;26786:1;26782:14;26778:2;26774:23;26770:32;26756:46;;26825:7;26817:6;26814:19;26811:39;;;26846:1;26843;26836:12;26811:39;26878:2;26874;26870:11;26890:315;26906:6;26901:3;26898:15;26890:315;;;26992:3;26979:17;-1:-1:-1;;;;;27015:11:238;27012:35;27009:55;;;27060:1;27057;27050:12;27009:55;27089:73;27154:7;27149:2;27135:11;27131:2;27127:20;27123:29;27089:73;:::i;:::-;27077:86;;-1:-1:-1;27192:2:238;27183:12;;;;26923;26890:315;;;26894:3;27224:5;27214:15;;;;;;25986:1249;;;;;:::o;27240:579::-;27367:4;27373:6;27433:11;27420:25;27527:2;27523:7;27512:8;27496:14;27492:29;27488:43;27468:18;27464:68;27454:96;;27546:1;27543;27536:12;27454:96;27573:33;;27625:20;;;-1:-1:-1;;;;;;27657:30:238;;27654:50;;;27700:1;27697;27690:12;27654:50;27733:4;27721:17;;-1:-1:-1;27784:1:238;27780:14;;;27764;27760:35;27750:46;;27747:66;;;27809:1;27806;27799:12;27824:521;27901:4;27907:6;27967:11;27954:25;28061:2;28057:7;28046:8;28030:14;28026:29;28022:43;28002:18;27998:68;27988:96;;28080:1;28077;28070:12;27988:96;28107:33;;28159:20;;;-1:-1:-1;;;;;;28191:30:238;;28188:50;;;28234:1;28231;28224:12;28188:50;28267:4;28255:17;;-1:-1:-1;28298:14:238;28294:27;;;28284:38;;28281:58;;;28335:1;28332;28325:12;28350:135;28389:3;28410:17;;;28407:43;;28430:18;;:::i;:::-;-1:-1:-1;28477:1:238;28466:13;;28350:135::o;28490:186::-;28549:6;28602:2;28590:9;28581:7;28577:23;28573:32;28570:52;;;28618:1;28615;28608:12;28570:52;28641:29;28660:9;28641:29;:::i;29258:679::-;29559:6;29548:9;29541:25;29602:3;29597:2;29586:9;29582:18;29575:31;29522:4;29629:63;29687:3;29676:9;29672:19;29664:6;29656;29629:63;:::i;:::-;29740:9;29732:6;29728:22;29723:2;29712:9;29708:18;29701:50;29768;29811:6;29803;29795;29768:50;:::i;:::-;-1:-1:-1;;;;;29854:32:238;;;;29849:2;29834:18;;29827:60;-1:-1:-1;;29918:3:238;29903:19;29896:35;29760:58;29258:679;-1:-1:-1;;;;;29258:679:238:o", "linkReferences": {}, "immutableReferences": {"7522": [{"start": 878, "length": 32}, {"start": 3403, "length": 32}], "7525": [{"start": 1276, "length": 32}, {"start": 3193, "length": 32}], "7528": [{"start": 1218, "length": 32}, {"start": 5171, "length": 32}, {"start": 5221, "length": 32}], "9937": [{"start": 2237, "length": 32}]}}, "methodIdentifiers": {"acceptOwnership()": "79ba5097", "approveMessages((string,string,string,address,bytes32)[],(((address,uint128)[],uint128,bytes32),bytes[]))": "64f1d85a", "callContract(string,string,bytes)": "1c92115f", "contractId()": "8291286c", "domainSeparator()": "f698da25", "epoch()": "900cf0cf", "epochBySignersHash(bytes32)": "4091c6d5", "implementation()": "5c60da1b", "isCommandExecuted(bytes32)": "d26ff210", "isContractCallApproved(bytes32,string,string,address,bytes32)": "f6a5f9f5", "isMessageApproved(string,string,string,address,bytes32)": "8f686dc4", "isMessageExecuted(string,string)": "cba5be6a", "lastRotationTimestamp()": "eb215f28", "messageHashToSign(bytes32,bytes32)": "fd721b47", "messageToCommandId(string,string)": "2fbceb4f", "minimumRotationDelay()": "f2213d37", "operator()": "570ca735", "owner()": "8da5cb5b", "pendingOwner()": "e30c3978", "previousSignersRetention()": "8c212aa5", "proposeOwnership(address)": "710bf322", "rotateSigners(((address,uint128)[],uint128,bytes32),(((address,uint128)[],uint128,bytes32),bytes[]))": "1d92c0bf", "setup(bytes)": "9ded06df", "signersHashByEpoch(uint256)": "d435b83e", "timeSinceRotation()": "377fff55", "transferOperatorship(address)": "4a6a42d8", "transferOwnership(address)": "f2fde38b", "upgrade(address,bytes32,bytes)": "a3499c73", "validateContractCall(bytes32,string,string,bytes32)": "5f6970c3", "validateMessage(string,string,string,bytes32)": "f10bfaa1", "validateProof(bytes32,(((address,uint128)[],uint128,bytes32),bytes[]))": "116399e4"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.26+commit.8a97fa7a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"previousSignersRetention_\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"domainSeparator_\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"minimumRotationDelay_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"signersHash\",\"type\":\"bytes32\"}],\"name\":\"DuplicateSigners\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"minimumRotationDelay\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"lastRotationTimestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"timeElapsed\",\"type\":\"uint256\"}],\"name\":\"InsufficientRotationDelay\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidCodeHash\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMessages\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidOwner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidOwnerAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidS\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"InvalidSender\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSignature\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSigners\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidThreshold\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidV\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidWeights\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LowSignaturesWeight\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MalformedSignatures\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotLatestSigners\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotOwner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotProxy\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"required\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"provided\",\"type\":\"uint256\"}],\"name\":\"RedundantSignaturesProvided\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SetupFailed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"destinationChain\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"destinationContractAddress\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"payload\",\"type\":\"bytes\"}],\"name\":\"ContractCall\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"commandId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"messageId\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"sourceAddress\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"contractAddress\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"MessageApproved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"commandId\",\"type\":\"bytes32\"}],\"name\":\"MessageExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"OperatorshipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferStarted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"epoch\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"signersHash\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"signers\",\"type\":\"bytes\"}],\"name\":\"SignersRotated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"messageId\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"sourceAddress\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"contractAddress\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"internalType\":\"struct Message[]\",\"name\":\"messages\",\"type\":\"tuple[]\"},{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"weight\",\"type\":\"uint128\"}],\"internalType\":\"struct WeightedSigner[]\",\"name\":\"signers\",\"type\":\"tuple[]\"},{\"internalType\":\"uint128\",\"name\":\"threshold\",\"type\":\"uint128\"},{\"internalType\":\"bytes32\",\"name\":\"nonce\",\"type\":\"bytes32\"}],\"internalType\":\"struct WeightedSigners\",\"name\":\"signers\",\"type\":\"tuple\"},{\"internalType\":\"bytes[]\",\"name\":\"signatures\",\"type\":\"bytes[]\"}],\"internalType\":\"struct Proof\",\"name\":\"proof\",\"type\":\"tuple\"}],\"name\":\"approveMessages\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"destinationChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"destinationContractAddress\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"payload\",\"type\":\"bytes\"}],\"name\":\"callContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"contractId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"domainSeparator\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"epoch\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"signersHash\",\"type\":\"bytes32\"}],\"name\":\"epochBySignersHash\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"implementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"implementation_\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"commandId\",\"type\":\"bytes32\"}],\"name\":\"isCommandExecuted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"commandId\",\"type\":\"bytes32\"},{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"sourceAddress\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"contractAddress\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"isContractCallApproved\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"messageId\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"sourceAddress\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"contractAddress\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"isMessageApproved\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"messageId\",\"type\":\"string\"}],\"name\":\"isMessageExecuted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastRotationTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"signersHash\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"dataHash\",\"type\":\"bytes32\"}],\"name\":\"messageHashToSign\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"messageId\",\"type\":\"string\"}],\"name\":\"messageToCommandId\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minimumRotationDelay\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"operator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"owner_\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingOwner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"owner_\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"previousSignersRetention\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"proposeOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"weight\",\"type\":\"uint128\"}],\"internalType\":\"struct WeightedSigner[]\",\"name\":\"signers\",\"type\":\"tuple[]\"},{\"internalType\":\"uint128\",\"name\":\"threshold\",\"type\":\"uint128\"},{\"internalType\":\"bytes32\",\"name\":\"nonce\",\"type\":\"bytes32\"}],\"internalType\":\"struct WeightedSigners\",\"name\":\"newSigners\",\"type\":\"tuple\"},{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"weight\",\"type\":\"uint128\"}],\"internalType\":\"struct WeightedSigner[]\",\"name\":\"signers\",\"type\":\"tuple[]\"},{\"internalType\":\"uint128\",\"name\":\"threshold\",\"type\":\"uint128\"},{\"internalType\":\"bytes32\",\"name\":\"nonce\",\"type\":\"bytes32\"}],\"internalType\":\"struct WeightedSigners\",\"name\":\"signers\",\"type\":\"tuple\"},{\"internalType\":\"bytes[]\",\"name\":\"signatures\",\"type\":\"bytes[]\"}],\"internalType\":\"struct Proof\",\"name\":\"proof\",\"type\":\"tuple\"}],\"name\":\"rotateSigners\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"setup\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"signerEpoch\",\"type\":\"uint256\"}],\"name\":\"signersHashByEpoch\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"timeSinceRotation\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOperator\",\"type\":\"address\"}],\"name\":\"transferOperatorship\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"newImplementationCodeHash\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"params\",\"type\":\"bytes\"}],\"name\":\"upgrade\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"commandId\",\"type\":\"bytes32\"},{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"sourceAddress\",\"type\":\"string\"},{\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"validateContractCall\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"valid\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"sourceChain\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"messageId\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"sourceAddress\",\"type\":\"string\"},{\"internalType\":\"bytes32\",\"name\":\"payloadHash\",\"type\":\"bytes32\"}],\"name\":\"validateMessage\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"valid\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"dataHash\",\"type\":\"bytes32\"},{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"weight\",\"type\":\"uint128\"}],\"internalType\":\"struct WeightedSigner[]\",\"name\":\"signers\",\"type\":\"tuple[]\"},{\"internalType\":\"uint128\",\"name\":\"threshold\",\"type\":\"uint128\"},{\"internalType\":\"bytes32\",\"name\":\"nonce\",\"type\":\"bytes32\"}],\"internalType\":\"struct WeightedSigners\",\"name\":\"signers\",\"type\":\"tuple\"},{\"internalType\":\"bytes[]\",\"name\":\"signatures\",\"type\":\"bytes[]\"}],\"internalType\":\"struct Proof\",\"name\":\"proof\",\"type\":\"tuple\"}],\"name\":\"validateProof\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isLatestSigners\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"ContractCall(address,string,string,bytes32,bytes)\":{\"details\":\"Logs the attempt to call a contract on another chain.\",\"params\":{\"destinationChain\":\"The name of the destination chain.\",\"destinationContractAddress\":\"The address of the contract on the destination chain.\",\"payload\":\"The payload data used for the contract call.\",\"payloadHash\":\"The keccak256 hash of the sent payload data.\",\"sender\":\"The address of the sender who initiated the contract call.\"}},\"MessageApproved(bytes32,string,string,string,address,bytes32)\":{\"params\":{\"commandId\":\"The identifier of the command to execute.\",\"contractAddress\":\"The address of the contract where the call will be executed.\",\"messageId\":\"The message id for the message.\",\"payloadHash\":\"The keccak256 hash of the approved payload data.\",\"sourceAddress\":\"The address of the sender on the source chain.\",\"sourceChain\":\"The name of the source chain from whence the command came.\"}},\"MessageExecuted(bytes32)\":{\"details\":\"Logs the execution of an approved message. `sourceChain` and `messageId` aren't included in the event due to backwards compatibility with `validateContractCall`.\",\"params\":{\"commandId\":\"The commandId for the message that was executed.\"}}},\"kind\":\"dev\",\"methods\":{\"acceptOwnership()\":{\"details\":\"Can only be called by the pending owner\"},\"approveMessages((string,string,string,address,bytes32)[],(((address,uint128)[],uint128,bytes32),bytes[]))\":{\"params\":{\"messages\":\"The array of messages to verify.\",\"proof\":\"The proof signed by the Axelar signers for this command.\"}},\"callContract(string,string,bytes)\":{\"params\":{\"destinationChain\":\"The chain where the destination contract exists. A registered chain name on Axelar must be used here\",\"destinationContractAddress\":\"The address of the contract to call on the destination chain\",\"payload\":\"The payload to be sent to the destination contract\"}},\"constructor\":{\"details\":\"Initializes the contract.\",\"params\":{\"domainSeparator_\":\"The domain separator for the signer proof\",\"minimumRotationDelay_\":\"The minimum delay required between rotations\",\"previousSignersRetention_\":\"The number of previous signers to retain\"}},\"contractId()\":{\"details\":\"Meant to be overridden in derived contracts.\",\"returns\":{\"_0\":\"bytes32 The contract ID\"}},\"epoch()\":{\"returns\":{\"_0\":\"uint256 The current signers epoch\"}},\"epochBySignersHash(bytes32)\":{\"params\":{\"signersHash\":\"The signers hash\"},\"returns\":{\"_0\":\"uint256 The epoch for the given signers hash\"}},\"implementation()\":{\"returns\":{\"implementation_\":\"Address of the current implementation\"}},\"isCommandExecuted(bytes32)\":{\"details\":\"The below methods are available for backwards compatibility with the original AxelarExecutable Other implementations can skip these methods.\"},\"isContractCallApproved(bytes32,string,string,address,bytes32)\":{\"details\":\"Determines whether a given contract call, identified by the commandId and payloadHash, is approved.\",\"params\":{\"commandId\":\"The identifier of the command to check.\",\"contractAddress\":\"The address of the contract where the call will be executed.\",\"payloadHash\":\"The keccak256 hash of the payload data.\",\"sourceAddress\":\"The address of the sender on the source chain.\",\"sourceChain\":\"The name of the source chain.\"},\"returns\":{\"_0\":\"True if the contract call is approved, false otherwise.\"}},\"isMessageApproved(string,string,string,address,bytes32)\":{\"details\":\"Determines whether a given message, identified by the sourceChain and messageId, is approved.\",\"params\":{\"contractAddress\":\"The address of the contract where the call will be executed.\",\"messageId\":\"The unique identifier of the message.\",\"payloadHash\":\"The keccak256 hash of the payload data.\",\"sourceAddress\":\"The address of the sender on the source chain.\",\"sourceChain\":\"The name of the source chain.\"},\"returns\":{\"_0\":\"True if the contract call is approved, false otherwise.\"}},\"isMessageExecuted(string,string)\":{\"details\":\"Determines whether a given message, identified by the sourceChain and messageId is executed.\",\"params\":{\"messageId\":\"The unique identifier of the message.\",\"sourceChain\":\"The name of the source chain.\"},\"returns\":{\"_0\":\"True if the message is executed, false otherwise.\"}},\"lastRotationTimestamp()\":{\"returns\":{\"_0\":\"uint256 The last rotation timestamp\"}},\"messageHashToSign(bytes32,bytes32)\":{\"details\":\"Returns an Ethereum Signed Message, created from `domainSeparator`, `signersHash`, and `dataHash`. This replicates the behavior of the https://github.com/ethereum/wiki/wiki/JSON-RPC#eth_sign[`eth_sign`] JSON-RPC method. See {recover}.\",\"params\":{\"dataHash\":\"The hash of the data\",\"signersHash\":\"The hash of the weighted signers that sign off on the data\"},\"returns\":{\"_0\":\"The message hash to be signed\"}},\"messageToCommandId(string,string)\":{\"params\":{\"messageId\":\"The unique message id for the message.\",\"sourceChain\":\"The name of the source chain as registered on Axelar.\"},\"returns\":{\"_0\":\"The commandId for the message.\"}},\"operator()\":{\"returns\":{\"_0\":\"The address of the operator.\"}},\"owner()\":{\"returns\":{\"owner_\":\"The current owner of the contract\"}},\"pendingOwner()\":{\"returns\":{\"owner_\":\"The pending owner of the contract\"}},\"proposeOwnership(address)\":{\"details\":\"Can only be called by the current owner. The ownership does not change until the new owner accepts the ownership transfer.\",\"params\":{\"newOwner\":\"The address to transfer ownership to\"}},\"rotateSigners(((address,uint128)[],uint128,bytes32),(((address,uint128)[],uint128,bytes32),bytes[]))\":{\"details\":\"The minimum rotation delay is enforced by default, unless the caller is the gateway operator. The gateway operator allows recovery in case of an incorrect/malicious rotation, while still requiring a valid proof from a recent signer set. Rotation to duplicate signers is rejected.\",\"params\":{\"newSigners\":\"The data for the new signers.\",\"proof\":\"The proof signed by the Axelar verifiers for this command.\"}},\"setup(bytes)\":{\"details\":\"This function is only callable by the proxy contract.\",\"params\":{\"data\":\"Initialization data for the contract\"}},\"signersHashByEpoch(uint256)\":{\"params\":{\"signerEpoch\":\"The given epoch\"},\"returns\":{\"_0\":\"bytes32 The signers hash for the given epoch\"}},\"timeSinceRotation()\":{\"returns\":{\"_0\":\"uint256 The time since the last rotation\"}},\"transferOperatorship(address)\":{\"details\":\"The owner or current operator can set the operator to address 0.\",\"params\":{\"newOperator\":\"The address of the new operator.\"}},\"transferOwnership(address)\":{\"details\":\"Can only be called by the current owner.\",\"params\":{\"newOwner\":\"The address to transfer ownership to\"}},\"upgrade(address,bytes32,bytes)\":{\"details\":\"This function is only callable by the owner.\",\"params\":{\"newImplementation\":\"The address of the new implementation contract\",\"newImplementationCodeHash\":\"The codehash of the new implementation contract\",\"params\":\"Optional setup parameters for the new implementation contract\"}},\"validateContractCall(bytes32,string,string,bytes32)\":{\"details\":\"Validates the given contract call information and marks it as approved if valid.\",\"params\":{\"commandId\":\"The identifier of the command to validate.\",\"payloadHash\":\"The keccak256 hash of the payload data.\",\"sourceAddress\":\"The address of the sender on the source chain.\",\"sourceChain\":\"The name of the source chain.\"},\"returns\":{\"valid\":\"True if the contract call is validated and approved, false otherwise.\"}},\"validateMessage(string,string,string,bytes32)\":{\"params\":{\"messageId\":\"The unique identifier of the message.\",\"payloadHash\":\"The keccak256 hash of the payload data.\",\"sourceAddress\":\"The address of the sender on the source chain.\",\"sourceChain\":\"The name of the source chain.\"},\"returns\":{\"valid\":\"True if the message is approved, false otherwise.\"}},\"validateProof(bytes32,(((address,uint128)[],uint128,bytes32),bytes[]))\":{\"params\":{\"dataHash\":\"The hash of the data being signed\",\"proof\":\"The proof from Axelar signers\"},\"returns\":{\"isLatestSigners\":\"True if provided signers are the current ones\"}}},\"stateVariables\":{\"AXELAR_AMPLIFIER_GATEWAY_SLOT\":{\"details\":\"This slot contains the storage for this contract in an upgrade-compatible manner keccak256('AxelarAmplifierGateway.Slot') - 1;\"}},\"title\":\"AxelarAmplifierGateway\",\"version\":1},\"userdoc\":{\"events\":{\"ContractCall(address,string,string,bytes32,bytes)\":{\"notice\":\"Emitted when a contract call is made through the gateway.\"},\"MessageApproved(bytes32,string,string,string,address,bytes32)\":{\"notice\":\"Emitted when a cross-chain message is approved.\"},\"MessageExecuted(bytes32)\":{\"notice\":\"Emitted when a message has been executed.\"}},\"kind\":\"user\",\"methods\":{\"acceptOwnership()\":{\"notice\":\"Accepts ownership of the contract.\"},\"approveMessages((string,string,string,address,bytes32)[],(((address,uint128)[],uint128,bytes32),bytes[]))\":{\"notice\":\"Approves an array of messages, signed by the Axelar signers.\"},\"callContract(string,string,bytes)\":{\"notice\":\"Sends a message to the specified destination chain and address with a given payload. This function is the entry point for general message passing between chains.\"},\"contractId()\":{\"notice\":\"Returns the contract ID. It can be used as a check during upgrades.\"},\"epoch()\":{\"notice\":\"This function returns the current signers epoch\"},\"epochBySignersHash(bytes32)\":{\"notice\":\"This function returns the epoch for a given signers hash\"},\"implementation()\":{\"notice\":\"Returns the address of the current implementation\"},\"isContractCallApproved(bytes32,string,string,address,bytes32)\":{\"notice\":\"Checks if a contract call is approved.\"},\"isMessageApproved(string,string,string,address,bytes32)\":{\"notice\":\"Checks if a message is approved.\"},\"isMessageExecuted(string,string)\":{\"notice\":\"Checks if a message is executed.\"},\"lastRotationTimestamp()\":{\"notice\":\"This function returns the timestamp for the last signer rotation\"},\"messageHashToSign(bytes32,bytes32)\":{\"notice\":\"Compute the message hash that is signed by the weighted signers\"},\"messageToCommandId(string,string)\":{\"notice\":\"Compute the commandId for a message.\"},\"operator()\":{\"notice\":\"Returns the address of the gateway operator.\"},\"owner()\":{\"notice\":\"Returns the current owner of the contract.\"},\"pendingOwner()\":{\"notice\":\"Returns the pending owner of the contract.\"},\"proposeOwnership(address)\":{\"notice\":\"Propose to transfer ownership of the contract to a new account `newOwner`.\"},\"rotateSigners(((address,uint128)[],uint128,bytes32),(((address,uint128)[],uint128,bytes32),bytes[]))\":{\"notice\":\"Rotate the weighted signers, signed off by the latest Axelar signers.\"},\"setup(bytes)\":{\"notice\":\"Sets up the contract with initial data\"},\"signersHashByEpoch(uint256)\":{\"notice\":\"This function returns the signers hash for a given epoch\"},\"timeSinceRotation()\":{\"notice\":\"This function returns the time elapsed (in secs) since the last rotation\"},\"transferOperatorship(address)\":{\"notice\":\"Transfer the operatorship to a new address.\"},\"transferOwnership(address)\":{\"notice\":\"Transfers ownership of the contract to a new account `newOwner`.\"},\"upgrade(address,bytes32,bytes)\":{\"notice\":\"Upgrades the contract to a new implementation\"},\"validateContractCall(bytes32,string,string,bytes32)\":{\"notice\":\"Validates and approves a contract call.\"},\"validateMessage(string,string,string,bytes32)\":{\"notice\":\"Validates if a message is approved. If message was in approved status, status is updated to executed to avoid replay.\"},\"validateProof(bytes32,(((address,uint128)[],uint128,bytes32),bytes[]))\":{\"notice\":\"This function takes dataHash and proof and reverts if proof is invalid\"}},\"notice\":\"AxelarAmplifierGateway is the contract that allows apps on EVM chains to send and receive cross-chain messages via the Axelar Amplifier protocol. It handles cross-chain message passing (implemented by BaseAmplifierGateway), and signer rotation (implemented by BaseWeightedMultisig).\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/AxelarAmplifierGateway.sol\":\"AxelarAmplifierGateway\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"none\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/\",\":@axelar-network/=node_modules/@axelar-network/\",\":@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/\",\":@openzeppelin/=node_modules/@openzeppelin/\",\":@uniswap/=node_modules/@uniswap/\",\":ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/\",\":forge-std/=node_modules/forge-std/src/\",\":recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/\",\":solady/=node_modules/solady/src/\",\":telcoin-contracts/=node_modules/telcoin-contracts/\"]},\"sources\":{\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/AxelarAmplifierGateway.sol\":{\"keccak256\":\"0xf5d6e5809b6cd7e4e45e23df6d1b1a26569e7075572a43e42c4b0531b2e7ce90\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1a1e132c340b07ed209ae38bfa4811104811d362f951bb9a3073f7859ed0d5c9\",\"dweb:/ipfs/QmZEf4DeMPP9XgQxkdcmXNVzg7ihUxu9bXjsndYFxpnMcj\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/BaseAmplifierGateway.sol\":{\"keccak256\":\"0x28c8874dc055be5eb2ca3c00a6f8aeb8dd7c53cb90915b5808795aecb1b94911\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7fde2bdfa495af1b71872e59050061efafa80c368893c09b9dc04b4085cf553d\",\"dweb:/ipfs/QmWma1wrzHcYPDe2zZ7uLYMqpR3JG4mN27PMA2wGUouGkM\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/governance/BaseWeightedMultisig.sol\":{\"keccak256\":\"0xb87baa31e24397fb0f1e6ff7392c3feca92d9ae7ba75d65db2667da1275508b5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cfae38c21719b13f2880e9aab9e2a2f3288510e104e5afef15099f3278182666\",\"dweb:/ipfs/QmTGzbYfDimwQqQUMjMnE5ENmAMrjWhgPf3uf7NWroYmXL\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IAxelarAmplifierGateway.sol\":{\"keccak256\":\"0xa6ac5ea5a0d6b6a0009d4e6c14e916d4a8dd7a7562ef12fc81cad29c1ae91c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cbbcd2648acfb758c4fe29dbaa2d66d14a7b0df971772e140528483697e643e7\",\"dweb:/ipfs/QmVCbBM5F8v3ycsw7ULG2PkXwtE7K62xMGNcf8xfhNdeNc\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IAxelarGateway.sol\":{\"keccak256\":\"0xbd92798eade563d8a663db59f6f04db5acdee5beda3a6d091e44a7d5c6b2f68e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1519882aed7b6de2ee1e316e5188d2bda1e305fc18a96b3895831f49dc9f90b0\",\"dweb:/ipfs/QmYTLwLGwmDkyX6GSE6PU93TkWP5mFz8qhmFyBrWfj6gaB\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IBaseAmplifierGateway.sol\":{\"keccak256\":\"0x6677f82aaf7fd8362ac21e1eb2f3c75b9ccfbb69d01d308f9e4a9811b612675f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://aa2899843c22bf3884eb08575a1bca2bc6524c93fc8ec9c47cc2a6bd1956fe19\",\"dweb:/ipfs/QmPGgPUWuxHypa8smMj9nupzCoCeGg7UpNqqucmP1xzwg4\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IBaseWeightedMultisig.sol\":{\"keccak256\":\"0x425e3ab621cd02ce224b07970dd99927ba9e33345337225c3250b9fc18c0267e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a9341e2cb46a60cbd4764f8506b409af65298d3125cd6db493035afb509bf638\",\"dweb:/ipfs/Qmb2aBMb3y5zXPC4L4ecCTgrw4issZo5cywZtYvwEMLkvT\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IContractIdentifier.sol\":{\"keccak256\":\"0x21a94655f7f1ed3d7188bd38ce6187892c615b80f9a74e123cbc38113d855a35\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e7e1f30e159d40a9f8431c26955a584507bc23a30f19380c723acdb4182ba7c1\",\"dweb:/ipfs/QmXb965di5pU3m3W36k19ZX8m6qCDaEi1jEt6NocqrGPXj\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IImplementation.sol\":{\"keccak256\":\"0xa7f275514d44bec3ab8ab8c768736329174d7cc6c5cd329c3509a66bce9cd468\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://983e8c97e4916b47244d71fa104fc368100991561fe8c28f1a6bae043763754d\",\"dweb:/ipfs/QmeFeJsCowShVsAe6RMiz3qjY2JTyRNYRRu1oYQcN9T6uV\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IOwnable.sol\":{\"keccak256\":\"0x5cb5691c9293e3011e1b133485411e8933ca1e110f2436a663df12991d134bf7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cdbeb0f7ec37155f976a3bdd9f13055c80f1b2d17043740d387403cb8d65a096\",\"dweb:/ipfs/QmXKLyWetJGgwUuEtexjPxfeZdgUywhrHS4898fQSFkC5y\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IUpgradable.sol\":{\"keccak256\":\"0x4e1f84c9711c3c15c77aee5fce8de99fcb49a902c7e6ff24626c924a60b8c22d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b2f36d8fb4b365e78663579bfd0d3252a6e290f6dd763d9275f4c6f5f51577d9\",\"dweb:/ipfs/QmZjhnPbjqnRztwbYYs1BdxRbSsAmSLkCfE9SNRk4jMFMe\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/libs/ECDSA.sol\":{\"keccak256\":\"0xfe7712e7cb5484d4cad2a479a5c4ff0ae0d104b5bbf93288c8ee2aef2cd5e184\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8a77057151845b95cc84d29e546893921ec65ae16808342a819b7bfc6036e61e\",\"dweb:/ipfs/QmWYTMuqierwdTMA1HomkZsKzF4xhr9o7779Erk9W4Q2mn\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/types/AmplifierGatewayTypes.sol\":{\"keccak256\":\"0xe56d68a7decb9e5e2b0cca59389885a3f5d68cea151f88968a1c753fd33b5319\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://93046232ee1a4727f27ade6f5f526921a3ca96d8ea372c176fada0cf4c670f8c\",\"dweb:/ipfs/QmRJ3rtBi312htUi2WPdHt4g3CKWsWqz6bEYJiEJ6fPrjS\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/types/WeightedMultisigTypes.sol\":{\"keccak256\":\"0x7dd2b6daf8e4756916ae643e761e3f1b79726cf623a5e42aa59adae6786cfdab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cc19b5e739146b3f75bd6c82755f3704dba7fdd50917bf452fad1bcb11f4949e\",\"dweb:/ipfs/Qmdo72haq6k7VQLUtStECpFbHyyU4UWxqegjweo1FzXMAf\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/upgradable/Implementation.sol\":{\"keccak256\":\"0xa194633594ef20445fe09496845e4b501398a80f364e3a1d05dc5ff5625197dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a35a3196b9aab5c2af7837477d2b7bbd20ee352861652fb7c9c94f3919266853\",\"dweb:/ipfs/QmWMmi2CvwikABehcjwnhbsAZpcktmWcvT7LM7s8gj949q\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/upgradable/Upgradable.sol\":{\"keccak256\":\"0xd7069f5936121c4b84f7bb9dd45460196875ff9bdf43a443b716f633e0ca4e21\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f93454b2a956169f8c99a774ad8d5ea8c61f1f7a6962fa0846301d9f777b8ec0\",\"dweb:/ipfs/QmRXPRGZFGTnWz3FTzXCKviziwqmaMzn8kRhHrsZhcj1aT\"]},\"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/utils/Ownable.sol\":{\"keccak256\":\"0xec5dff9da8b33e3d96cac47303e387876cee4139bdc719d775f641a8bb9f604c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ee3e8e849eb5edad49f09182ea994f10495faa6af3794630475428753c50cc23\",\"dweb:/ipfs/Qme7xmsnZhCfi96yLeS8m9DewDskqzbpCWGefh2h8ZDfTw\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.26+commit.8a97fa7a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "previousSignersRetention_", "type": "uint256"}, {"internalType": "bytes32", "name": "domainSeparator_", "type": "bytes32"}, {"internalType": "uint256", "name": "minimumRotationDelay_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "bytes32", "name": "signersHash", "type": "bytes32"}], "type": "error", "name": "DuplicateSigners"}, {"inputs": [{"internalType": "uint256", "name": "minimumRotationDelay", "type": "uint256"}, {"internalType": "uint256", "name": "lastRotationTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "timeElapsed", "type": "uint256"}], "type": "error", "name": "InsufficientRotationDelay"}, {"inputs": [], "type": "error", "name": "InvalidCodeHash"}, {"inputs": [], "type": "error", "name": "InvalidImplementation"}, {"inputs": [], "type": "error", "name": "InvalidMessages"}, {"inputs": [], "type": "error", "name": "InvalidOwner"}, {"inputs": [], "type": "error", "name": "InvalidOwnerAddress"}, {"inputs": [], "type": "error", "name": "InvalidS"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "InvalidSender"}, {"inputs": [], "type": "error", "name": "InvalidSignature"}, {"inputs": [], "type": "error", "name": "InvalidSignatureLength"}, {"inputs": [], "type": "error", "name": "InvalidSigners"}, {"inputs": [], "type": "error", "name": "InvalidThreshold"}, {"inputs": [], "type": "error", "name": "InvalidV"}, {"inputs": [], "type": "error", "name": "InvalidWeights"}, {"inputs": [], "type": "error", "name": "LowSignaturesWeight"}, {"inputs": [], "type": "error", "name": "MalformedSignatures"}, {"inputs": [], "type": "error", "name": "NotLatestSigners"}, {"inputs": [], "type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [], "type": "error", "name": "NotProxy"}, {"inputs": [{"internalType": "uint256", "name": "required", "type": "uint256"}, {"internalType": "uint256", "name": "provided", "type": "uint256"}], "type": "error", "name": "RedundantSignaturesProvided"}, {"inputs": [], "type": "error", "name": "SetupFailed"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "string", "name": "destinationChain", "type": "string", "indexed": false}, {"internalType": "string", "name": "destinationContractAddress", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32", "indexed": true}, {"internalType": "bytes", "name": "payload", "type": "bytes", "indexed": false}], "type": "event", "name": "ContractCall", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "commandId", "type": "bytes32", "indexed": true}, {"internalType": "string", "name": "sourceChain", "type": "string", "indexed": false}, {"internalType": "string", "name": "messageId", "type": "string", "indexed": false}, {"internalType": "string", "name": "sourceAddress", "type": "string", "indexed": false}, {"internalType": "address", "name": "contractAddress", "type": "address", "indexed": true}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32", "indexed": true}], "type": "event", "name": "MessageApproved", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "commandId", "type": "bytes32", "indexed": true}], "type": "event", "name": "MessageExecuted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newOperator", "type": "address", "indexed": false}], "type": "event", "name": "OperatorshipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferStarted", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "epoch", "type": "uint256", "indexed": true}, {"internalType": "bytes32", "name": "signersHash", "type": "bytes32", "indexed": true}, {"internalType": "bytes", "name": "signers", "type": "bytes", "indexed": false}], "type": "event", "name": "SignersRotated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "acceptOwnership"}, {"inputs": [{"internalType": "struct Message[]", "name": "messages", "type": "tuple[]", "components": [{"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "messageId", "type": "string"}, {"internalType": "string", "name": "sourceAddress", "type": "string"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32"}]}, {"internalType": "struct Proof", "name": "proof", "type": "tuple", "components": [{"internalType": "struct WeightedSigners", "name": "signers", "type": "tuple", "components": [{"internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "name": "signers", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint128", "name": "weight", "type": "uint128"}]}, {"internalType": "uint128", "name": "threshold", "type": "uint128"}, {"internalType": "bytes32", "name": "nonce", "type": "bytes32"}]}, {"internalType": "bytes[]", "name": "signatures", "type": "bytes[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "approveMessages"}, {"inputs": [{"internalType": "string", "name": "destinationChain", "type": "string"}, {"internalType": "string", "name": "destinationContractAddress", "type": "string"}, {"internalType": "bytes", "name": "payload", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "callContract"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "contractId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "domainSeparator", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "epoch", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "signersHash", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "epochBySignersHash", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "implementation", "outputs": [{"internalType": "address", "name": "implementation_", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "commandId", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "isCommandExecuted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes32", "name": "commandId", "type": "bytes32"}, {"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "sourceAddress", "type": "string"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "isContractCallApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "messageId", "type": "string"}, {"internalType": "string", "name": "sourceAddress", "type": "string"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "isMessageApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "messageId", "type": "string"}], "stateMutability": "view", "type": "function", "name": "isMessageExecuted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lastRotationTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "signersHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "messageHashToSign", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "messageId", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "messageToCommandId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "minimumRotationDelay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "owner_", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pending<PERSON><PERSON>er", "outputs": [{"internalType": "address", "name": "owner_", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "previousSignersRetention", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "proposeOwnership"}, {"inputs": [{"internalType": "struct WeightedSigners", "name": "newSigners", "type": "tuple", "components": [{"internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "name": "signers", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint128", "name": "weight", "type": "uint128"}]}, {"internalType": "uint128", "name": "threshold", "type": "uint128"}, {"internalType": "bytes32", "name": "nonce", "type": "bytes32"}]}, {"internalType": "struct Proof", "name": "proof", "type": "tuple", "components": [{"internalType": "struct WeightedSigners", "name": "signers", "type": "tuple", "components": [{"internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "name": "signers", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint128", "name": "weight", "type": "uint128"}]}, {"internalType": "uint128", "name": "threshold", "type": "uint128"}, {"internalType": "bytes32", "name": "nonce", "type": "bytes32"}]}, {"internalType": "bytes[]", "name": "signatures", "type": "bytes[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "rotateSigners"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "setup"}, {"inputs": [{"internalType": "uint256", "name": "signer<PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "signersHashByEpoch", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "timeSinceRotation", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "newOperator", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOperatorship"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes32", "name": "newImplementationCodeHash", "type": "bytes32"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "upgrade"}, {"inputs": [{"internalType": "bytes32", "name": "commandId", "type": "bytes32"}, {"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "sourceAddress", "type": "string"}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "validateContractCall", "outputs": [{"internalType": "bool", "name": "valid", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "sourceChain", "type": "string"}, {"internalType": "string", "name": "messageId", "type": "string"}, {"internalType": "string", "name": "sourceAddress", "type": "string"}, {"internalType": "bytes32", "name": "payloadHash", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "validateMessage", "outputs": [{"internalType": "bool", "name": "valid", "type": "bool"}]}, {"inputs": [{"internalType": "bytes32", "name": "dataHash", "type": "bytes32"}, {"internalType": "struct Proof", "name": "proof", "type": "tuple", "components": [{"internalType": "struct WeightedSigners", "name": "signers", "type": "tuple", "components": [{"internalType": "struct Weighted<PERSON><PERSON><PERSON>[]", "name": "signers", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint128", "name": "weight", "type": "uint128"}]}, {"internalType": "uint128", "name": "threshold", "type": "uint128"}, {"internalType": "bytes32", "name": "nonce", "type": "bytes32"}]}, {"internalType": "bytes[]", "name": "signatures", "type": "bytes[]"}]}], "stateMutability": "view", "type": "function", "name": "validateProof", "outputs": [{"internalType": "bool", "name": "isLatestSigners", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"acceptOwnership()": {"details": "Can only be called by the pending owner"}, "approveMessages((string,string,string,address,bytes32)[],(((address,uint128)[],uint128,bytes32),bytes[]))": {"params": {"messages": "The array of messages to verify.", "proof": "The proof signed by the <PERSON><PERSON> signers for this command."}}, "callContract(string,string,bytes)": {"params": {"destinationChain": "The chain where the destination contract exists. A registered chain name on Axelar must be used here", "destinationContractAddress": "The address of the contract to call on the destination chain", "payload": "The payload to be sent to the destination contract"}}, "constructor": {"details": "Initializes the contract.", "params": {"domainSeparator_": "The domain separator for the signer proof", "minimumRotationDelay_": "The minimum delay required between rotations", "previousSignersRetention_": "The number of previous signers to retain"}}, "contractId()": {"details": "Meant to be overridden in derived contracts.", "returns": {"_0": "bytes32 The contract ID"}}, "epoch()": {"returns": {"_0": "uint256 The current signers epoch"}}, "epochBySignersHash(bytes32)": {"params": {"signersHash": "The signers hash"}, "returns": {"_0": "uint256 The epoch for the given signers hash"}}, "implementation()": {"returns": {"implementation_": "Address of the current implementation"}}, "isCommandExecuted(bytes32)": {"details": "The below methods are available for backwards compatibility with the original AxelarExecutable Other implementations can skip these methods."}, "isContractCallApproved(bytes32,string,string,address,bytes32)": {"details": "Determines whether a given contract call, identified by the commandId and payloadHash, is approved.", "params": {"commandId": "The identifier of the command to check.", "contractAddress": "The address of the contract where the call will be executed.", "payloadHash": "The keccak256 hash of the payload data.", "sourceAddress": "The address of the sender on the source chain.", "sourceChain": "The name of the source chain."}, "returns": {"_0": "True if the contract call is approved, false otherwise."}}, "isMessageApproved(string,string,string,address,bytes32)": {"details": "Determines whether a given message, identified by the source<PERSON><PERSON><PERSON> and messageId, is approved.", "params": {"contractAddress": "The address of the contract where the call will be executed.", "messageId": "The unique identifier of the message.", "payloadHash": "The keccak256 hash of the payload data.", "sourceAddress": "The address of the sender on the source chain.", "sourceChain": "The name of the source chain."}, "returns": {"_0": "True if the contract call is approved, false otherwise."}}, "isMessageExecuted(string,string)": {"details": "Determines whether a given message, identified by the source<PERSON><PERSON><PERSON> and messageId is executed.", "params": {"messageId": "The unique identifier of the message.", "sourceChain": "The name of the source chain."}, "returns": {"_0": "True if the message is executed, false otherwise."}}, "lastRotationTimestamp()": {"returns": {"_0": "uint256 The last rotation timestamp"}}, "messageHashToSign(bytes32,bytes32)": {"details": "Returns an Ethereum Signed Message, created from `domainSeparator`, `signersHash`, and `dataHash`. This replicates the behavior of the https://github.com/ethereum/wiki/wiki/JSON-RPC#eth_sign[`eth_sign`] JSON-RPC method. See {recover}.", "params": {"dataHash": "The hash of the data", "signersHash": "The hash of the weighted signers that sign off on the data"}, "returns": {"_0": "The message hash to be signed"}}, "messageToCommandId(string,string)": {"params": {"messageId": "The unique message id for the message.", "sourceChain": "The name of the source chain as registered on Axelar."}, "returns": {"_0": "The commandId for the message."}}, "operator()": {"returns": {"_0": "The address of the operator."}}, "owner()": {"returns": {"owner_": "The current owner of the contract"}}, "pendingOwner()": {"returns": {"owner_": "The pending owner of the contract"}}, "proposeOwnership(address)": {"details": "Can only be called by the current owner. The ownership does not change until the new owner accepts the ownership transfer.", "params": {"newOwner": "The address to transfer ownership to"}}, "rotateSigners(((address,uint128)[],uint128,bytes32),(((address,uint128)[],uint128,bytes32),bytes[]))": {"details": "The minimum rotation delay is enforced by default, unless the caller is the gateway operator. The gateway operator allows recovery in case of an incorrect/malicious rotation, while still requiring a valid proof from a recent signer set. Rotation to duplicate signers is rejected.", "params": {"newSigners": "The data for the new signers.", "proof": "The proof signed by the Axelar verifiers for this command."}}, "setup(bytes)": {"details": "This function is only callable by the proxy contract.", "params": {"data": "Initialization data for the contract"}}, "signersHashByEpoch(uint256)": {"params": {"signerEpoch": "The given epoch"}, "returns": {"_0": "bytes32 The signers hash for the given epoch"}}, "timeSinceRotation()": {"returns": {"_0": "uint256 The time since the last rotation"}}, "transferOperatorship(address)": {"details": "The owner or current operator can set the operator to address 0.", "params": {"newOperator": "The address of the new operator."}}, "transferOwnership(address)": {"details": "Can only be called by the current owner.", "params": {"newOwner": "The address to transfer ownership to"}}, "upgrade(address,bytes32,bytes)": {"details": "This function is only callable by the owner.", "params": {"newImplementation": "The address of the new implementation contract", "newImplementationCodeHash": "The codehash of the new implementation contract", "params": "Optional setup parameters for the new implementation contract"}}, "validateContractCall(bytes32,string,string,bytes32)": {"details": "Validates the given contract call information and marks it as approved if valid.", "params": {"commandId": "The identifier of the command to validate.", "payloadHash": "The keccak256 hash of the payload data.", "sourceAddress": "The address of the sender on the source chain.", "sourceChain": "The name of the source chain."}, "returns": {"valid": "True if the contract call is validated and approved, false otherwise."}}, "validateMessage(string,string,string,bytes32)": {"params": {"messageId": "The unique identifier of the message.", "payloadHash": "The keccak256 hash of the payload data.", "sourceAddress": "The address of the sender on the source chain.", "sourceChain": "The name of the source chain."}, "returns": {"valid": "True if the message is approved, false otherwise."}}, "validateProof(bytes32,(((address,uint128)[],uint128,bytes32),bytes[]))": {"params": {"dataHash": "The hash of the data being signed", "proof": "The proof from <PERSON><PERSON> signers"}, "returns": {"isLatestSigners": "True if provided signers are the current ones"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"acceptOwnership()": {"notice": "Accepts ownership of the contract."}, "approveMessages((string,string,string,address,bytes32)[],(((address,uint128)[],uint128,bytes32),bytes[]))": {"notice": "Approves an array of messages, signed by the Axelar signers."}, "callContract(string,string,bytes)": {"notice": "Sends a message to the specified destination chain and address with a given payload. This function is the entry point for general message passing between chains."}, "contractId()": {"notice": "Returns the contract ID. It can be used as a check during upgrades."}, "epoch()": {"notice": "This function returns the current signers epoch"}, "epochBySignersHash(bytes32)": {"notice": "This function returns the epoch for a given signers hash"}, "implementation()": {"notice": "Returns the address of the current implementation"}, "isContractCallApproved(bytes32,string,string,address,bytes32)": {"notice": "Checks if a contract call is approved."}, "isMessageApproved(string,string,string,address,bytes32)": {"notice": "Checks if a message is approved."}, "isMessageExecuted(string,string)": {"notice": "Checks if a message is executed."}, "lastRotationTimestamp()": {"notice": "This function returns the timestamp for the last signer rotation"}, "messageHashToSign(bytes32,bytes32)": {"notice": "Compute the message hash that is signed by the weighted signers"}, "messageToCommandId(string,string)": {"notice": "Compute the commandId for a message."}, "operator()": {"notice": "Returns the address of the gateway operator."}, "owner()": {"notice": "Returns the current owner of the contract."}, "pendingOwner()": {"notice": "Returns the pending owner of the contract."}, "proposeOwnership(address)": {"notice": "Propose to transfer ownership of the contract to a new account `newOwner`."}, "rotateSigners(((address,uint128)[],uint128,bytes32),(((address,uint128)[],uint128,bytes32),bytes[]))": {"notice": "Rotate the weighted signers, signed off by the latest Axelar signers."}, "setup(bytes)": {"notice": "Sets up the contract with initial data"}, "signersHashByEpoch(uint256)": {"notice": "This function returns the signers hash for a given epoch"}, "timeSinceRotation()": {"notice": "This function returns the time elapsed (in secs) since the last rotation"}, "transferOperatorship(address)": {"notice": "Transfer the operatorship to a new address."}, "transferOwnership(address)": {"notice": "Transfers ownership of the contract to a new account `newOwner`."}, "upgrade(address,bytes32,bytes)": {"notice": "Upgrades the contract to a new implementation"}, "validateContractCall(bytes32,string,string,bytes32)": {"notice": "Validates and approves a contract call."}, "validateMessage(string,string,string,bytes32)": {"notice": "Validates if a message is approved. If message was in approved status, status is updated to executed to avoid replay."}, "validateProof(bytes32,(((address,uint128)[],uint128,bytes32),bytes[]))": {"notice": "This function takes dataHash and proof and reverts if proof is invalid"}}, "version": 1}}, "settings": {"remappings": ["@axelar-cgp-solidity/=node_modules/@axelar-network/axelar-cgp-solidity/", "@axelar-network/=node_modules/@axelar-network/", "@openzeppelin-contracts/=node_modules/recoverable-wrapper/node_modules/@openzeppelin/contracts/", "@openzeppelin/=node_modules/@openzeppelin/", "@uniswap/=node_modules/@uniswap/", "ds-test/=node_modules/recoverable-wrapper/lib/forge-std/lib/ds-test/src/", "forge-std/=node_modules/forge-std/src/", "recoverable-wrapper/contracts/=node_modules/recoverable-wrapper/contracts/", "solady/=node_modules/solady/src/", "telcoin-contracts/=node_modules/telcoin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "none"}, "compilationTarget": {"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/AxelarAmplifierGateway.sol": "AxelarAmplifierGateway"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/AxelarAmplifierGateway.sol": {"keccak256": "0xf5d6e5809b6cd7e4e45e23df6d1b1a26569e7075572a43e42c4b0531b2e7ce90", "urls": ["bzz-raw://1a1e132c340b07ed209ae38bfa4811104811d362f951bb9a3073f7859ed0d5c9", "dweb:/ipfs/QmZEf4DeMPP9XgQxkdcmXNVzg7ihUxu9bXjsndYFxpnMcj"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/BaseAmplifierGateway.sol": {"keccak256": "0x28c8874dc055be5eb2ca3c00a6f8aeb8dd7c53cb90915b5808795aecb1b94911", "urls": ["bzz-raw://7fde2bdfa495af1b71872e59050061efafa80c368893c09b9dc04b4085cf553d", "dweb:/ipfs/QmWma1wrzHcYPDe2zZ7uLYMqpR3JG4mN27PMA2wGUouGkM"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/governance/BaseWeightedMultisig.sol": {"keccak256": "0xb87baa31e24397fb0f1e6ff7392c3feca92d9ae7ba75d65db2667da1275508b5", "urls": ["bzz-raw://cfae38c21719b13f2880e9aab9e2a2f3288510e104e5afef15099f3278182666", "dweb:/ipfs/QmTGzbYfDimwQqQUMjMnE5ENmAMrjWhgPf3uf7NWroYmXL"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IAxelarAmplifierGateway.sol": {"keccak256": "0xa6ac5ea5a0d6b6a0009d4e6c14e916d4a8dd7a7562ef12fc81cad29c1ae91c5e", "urls": ["bzz-raw://cbbcd2648acfb758c4fe29dbaa2d66d14a7b0df971772e140528483697e643e7", "dweb:/ipfs/QmVCbBM5F8v3ycsw7ULG2PkXwtE7K62xMGNcf8xfhNdeNc"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IAxelarGateway.sol": {"keccak256": "0xbd92798eade563d8a663db59f6f04db5acdee5beda3a6d091e44a7d5c6b2f68e", "urls": ["bzz-raw://1519882aed7b6de2ee1e316e5188d2bda1e305fc18a96b3895831f49dc9f90b0", "dweb:/ipfs/QmYTLwLGwmDkyX6GSE6PU93TkWP5mFz8qhmFyBrWfj6gaB"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IBaseAmplifierGateway.sol": {"keccak256": "0x6677f82aaf7fd8362ac21e1eb2f3c75b9ccfbb69d01d308f9e4a9811b612675f", "urls": ["bzz-raw://aa2899843c22bf3884eb08575a1bca2bc6524c93fc8ec9c47cc2a6bd1956fe19", "dweb:/ipfs/QmPGgPUWuxHypa8smMj9nupzCoCeGg7UpNqqucmP1xzwg4"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IBaseWeightedMultisig.sol": {"keccak256": "0x425e3ab621cd02ce224b07970dd99927ba9e33345337225c3250b9fc18c0267e", "urls": ["bzz-raw://a9341e2cb46a60cbd4764f8506b409af65298d3125cd6db493035afb509bf638", "dweb:/ipfs/Qmb2aBMb3y5zXPC4L4ecCTgrw4issZo5cywZtYvwEMLkvT"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IContractIdentifier.sol": {"keccak256": "0x21a94655f7f1ed3d7188bd38ce6187892c615b80f9a74e123cbc38113d855a35", "urls": ["bzz-raw://e7e1f30e159d40a9f8431c26955a584507bc23a30f19380c723acdb4182ba7c1", "dweb:/ipfs/QmXb965di5pU3m3W36k19ZX8m6qCDaEi1jEt6NocqrGPXj"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IImplementation.sol": {"keccak256": "0xa7f275514d44bec3ab8ab8c768736329174d7cc6c5cd329c3509a66bce9cd468", "urls": ["bzz-raw://983e8c97e4916b47244d71fa104fc368100991561fe8c28f1a6bae043763754d", "dweb:/ipfs/QmeFeJsCowShVsAe6RMiz3qjY2JTyRNYRRu1oYQcN9T6uV"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IOwnable.sol": {"keccak256": "0x5cb5691c9293e3011e1b133485411e8933ca1e110f2436a663df12991d134bf7", "urls": ["bzz-raw://cdbeb0f7ec37155f976a3bdd9f13055c80f1b2d17043740d387403cb8d65a096", "dweb:/ipfs/QmXKLyWetJGgwUuEtexjPxfeZdgUywhrHS4898fQSFkC5y"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/interfaces/IUpgradable.sol": {"keccak256": "0x4e1f84c9711c3c15c77aee5fce8de99fcb49a902c7e6ff24626c924a60b8c22d", "urls": ["bzz-raw://b2f36d8fb4b365e78663579bfd0d3252a6e290f6dd763d9275f4c6f5f51577d9", "dweb:/ipfs/QmZjhnPbjqnRztwbYYs1BdxRbSsAmSLkCfE9SNRk4jMFMe"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/libs/ECDSA.sol": {"keccak256": "0xfe7712e7cb5484d4cad2a479a5c4ff0ae0d104b5bbf93288c8ee2aef2cd5e184", "urls": ["bzz-raw://8a77057151845b95cc84d29e546893921ec65ae16808342a819b7bfc6036e61e", "dweb:/ipfs/QmWYTMuqierwdTMA1HomkZsKzF4xhr9o7779Erk9W4Q2mn"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/types/AmplifierGatewayTypes.sol": {"keccak256": "0xe56d68a7decb9e5e2b0cca59389885a3f5d68cea151f88968a1c753fd33b5319", "urls": ["bzz-raw://93046232ee1a4727f27ade6f5f526921a3ca96d8ea372c176fada0cf4c670f8c", "dweb:/ipfs/QmRJ3rtBi312htUi2WPdHt4g3CKWsWqz6bEYJiEJ6fPrjS"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/types/WeightedMultisigTypes.sol": {"keccak256": "0x7dd2b6daf8e4756916ae643e761e3f1b79726cf623a5e42aa59adae6786cfdab", "urls": ["bzz-raw://cc19b5e739146b3f75bd6c82755f3704dba7fdd50917bf452fad1bcb11f4949e", "dweb:/ipfs/Qmdo72haq6k7VQLUtStECpFbHyyU4UWxqegjweo1FzXMAf"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/upgradable/Implementation.sol": {"keccak256": "0xa194633594ef20445fe09496845e4b501398a80f364e3a1d05dc5ff5625197dd", "urls": ["bzz-raw://a35a3196b9aab5c2af7837477d2b7bbd20ee352861652fb7c9c94f3919266853", "dweb:/ipfs/QmWMmi2CvwikABehcjwnhbsAZpcktmWcvT7LM7s8gj949q"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/upgradable/Upgradable.sol": {"keccak256": "0xd7069f5936121c4b84f7bb9dd45460196875ff9bdf43a443b716f633e0ca4e21", "urls": ["bzz-raw://f93454b2a956169f8c99a774ad8d5ea8c61f1f7a6962fa0846301d9f777b8ec0", "dweb:/ipfs/QmRXPRGZFGTnWz3FTzXCKviziwqmaMzn8kRhHrsZhcj1aT"], "license": "MIT"}, "node_modules/@axelar-network/axelar-gmp-sdk-solidity/contracts/utils/Ownable.sol": {"keccak256": "0xec5dff9da8b33e3d96cac47303e387876cee4139bdc719d775f641a8bb9f604c", "urls": ["bzz-raw://ee3e8e849eb5edad49f09182ea994f10495faa6af3794630475428753c50cc23", "dweb:/ipfs/Qme7xmsnZhCfi96yLeS8m9DewDskqzbpCWGefh2h8ZDfTw"], "license": "MIT"}}, "version": 1}, "id": 27}