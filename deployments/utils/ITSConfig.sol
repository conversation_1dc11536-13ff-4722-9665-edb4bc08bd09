// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity ^0.8.20;

import { LibString } from "solady/utils/LibString.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { Create3Deployer } from "@axelar-network/axelar-gmp-sdk-solidity/contracts/deploy/Create3Deployer.sol";
import {
    WeightedSigner,
    WeightedSigners,
    Proof
} from "@axelar-network/axelar-gmp-sdk-solidity/contracts/types/WeightedMultisigTypes.sol";
import { AxelarAmplifierGateway } from "@axelar-network/axelar-gmp-sdk-solidity/contracts/gateway/AxelarAmplifierGateway.sol";
import { ITokenManagerType } from "@axelar-network/interchain-token-service/contracts/interfaces/ITokenManagerType.sol";
import { InterchainTokenService } from "@axelar-network/interchain-token-service/contracts/InterchainTokenService.sol";
import { InterchainTokenFactory } from "@axelar-network/interchain-token-service/contracts/InterchainTokenFactory.sol";
import { AddressBytes } from "@axelar-network/axelar-gmp-sdk-solidity/contracts/libs/AddressBytes.sol";
import { ITSUtils } from "./ITSUtils.sol";

abstract contract ITSConfig is ITSUtils {
    // chain constants
    string constant ITS_HUB_CHAIN_NAME = "axelar";
    string constant ITS_HUB_ROUTING_IDENTIFIER = "hub";
    string constant ITS_HUB_ROUTER_ADDR = "axelar157hl7gpuknjmhtac2qnphuazv2yerfagva7lsu9vuj2pgn32z22qa26dk4";
    string constant TN_CHAIN_NAME = "telcoin-network";
    bytes32 constant TN_CHAINNAMEHASH = keccak256(bytes(TN_CHAIN_NAME));
    string constant MAINNET_CHAIN_NAME = "Ethereum";
    bytes32 constant MAINNET_CHAINNAMEHASH = 0x564ccaf7594d66b1eaaea24fe01f0585bf52ee70852af4eac0cc4b04711cd0e2;
    address constant MAINNET_ITS = ******************************************;
    address constant MAINNET_GATEWAY = ******************************************;
    address constant MAINNET_TEL = ******************************************;
    uint256 constant SEPOLIA_CHAINID = ********;
    string constant DEVNET_SEPOLIA_CHAIN_NAME = "eth-sepolia";
    bytes32 constant DEVNET_SEPOLIA_CHAINNAMEHASH = 0x24f78f6b35533491ef3d467d5e8306033cca94049b9b76db747dfc786df43f86;
    address constant DEVNET_SEPOLIA_ITS = ******************************************;
    address constant DEVNET_SEPOLIA_GATEWAY = ******************************************;
    string constant TESTNET_SEPOLIA_CHAIN_NAME = "ethereum-sepolia";
    bytes32 constant TESTNET_SEPOLIA_CHAINNAMEHASH = 0x564ccaf7594d66b1eaaea24fe01f0585bf52ee70852af4eac0cc4b04711cd0e2;
    address constant TESTNET_SEPOLIA_ITS = ******************************************;
    address constant TESTNET_SEPOLIA_GATEWAY = ******************************************;

    // message type constants; these serve as headers for ITS messages between chains
    uint256 constant MESSAGE_TYPE_INTERCHAIN_TRANSFER = 0;
    uint256 constant MESSAGE_TYPE_DEPLOY_INTERCHAIN_TOKEN = 1;
    // uint256 constant MESSAGE_TYPE_DEPLOY_TOKEN_MANAGER = 2; // replaced with `linkToken() in v2.1.0`
    uint256 constant MESSAGE_TYPE_SEND_TO_HUB = 3;
    uint256 constant MESSAGE_TYPE_RECEIVE_FROM_HUB = 4;
    uint256 constant MESSAGE_TYPE_LINK_TOKEN = 5;
    uint256 constant MESSAGE_TYPE_REGISTER_TOKEN_METADATA = 6;

    // mutable fork contracts
    // Sepolia
    IERC20 sepoliaTEL;
    InterchainTokenService sepoliaITS;
    InterchainTokenFactory sepoliaITF;
    AxelarAmplifierGateway sepoliaGateway;

    uint256 public constant telTotalSupply = 100_000_000_000e18;

    /// @dev Create3 deployment of ITS requires some deterministic addresses before deployment
    /// @dev Prefetch target addrs for constructor args is also helpful for the config setups
    function _precalculateCreate3ConstructorArgs(Create3Deployer create3Deploy, address sender) internal view returns (address precalculatedITS, address precalculatedWTEL, address precalculatedInterchainTEL) {
        precalculatedITS = create3Deploy.deployedAddress("", sender, salts.itsSalt);
        precalculatedWTEL = create3Deploy.deployedAddress("", sender, salts.wtelSalt);
        precalculatedInterchainTEL = create3Deploy.deployedAddress("", sender, salts.itelSalt);
    }

    function _setUpDevnetConfig(address admin, address devnetTEL, address wtel, address itel) internal virtual {
        // devnet uses adminas linker and single verifier running tofnd + ampd
        linker = admin;
        address ampdVerifier = 0xCc9Cc353B765Fee36669Af494bDcdc8660402d32;

        // AxelarAmplifierGateway
        axelarId = TN_CHAIN_NAME;
        routerAddress = ITS_HUB_ROUTER_ADDR;
        telChainId = 0x7e1;
        domainSeparator = keccak256(abi.encodePacked(axelarId, routerAddress, telChainId));
        previousSignersRetention = 16;
        minimumRotationDelay = 86_400;
        weight = 1; 
        threshold = 1;
        nonce = bytes32(0x0);
        ampdVerifierSigners.push(ampdVerifier);
        signerArray.push(WeightedSigner(ampdVerifierSigners[0], weight));
        // in memory since nested arrays within custom Solidity structs cannot be copied to storage
        WeightedSigners memory weightedSigners = WeightedSigners(signerArray, threshold, nonce);
        WeightedSigners[] memory weightedSignersArray = new WeightedSigners[](1);
        weightedSignersArray[0] = weightedSigners;
        gatewayOperator = admin;
        gatewaySetupParams = abi.encode(gatewayOperator, weightedSignersArray);
        gatewayOwner = admin;

        // AxelarGasService
        gasCollector = admin;
        gasValue = 30_000_000;
        gsOwner = admin;
        gsSetupParams = ""; // note: unused

        // InterchainTokenService
        itsOwner = admin;
        itsOperator = admin;
        chainName_ = TN_CHAIN_NAME;
        trustedChainNames.push(ITS_HUB_CHAIN_NAME); // leverage ITS hub to support remote chains
        trustedChainNames.push(DEVNET_SEPOLIA_CHAIN_NAME);
        trustedChainNames.push(TN_CHAIN_NAME);
        trustedAddresses.push(ITS_HUB_ROUTING_IDENTIFIER);
        trustedAddresses.push(ITS_HUB_ROUTING_IDENTIFIER);
        trustedAddresses.push(ITS_HUB_ROUTING_IDENTIFIER);
        itsSetupParams = abi.encode(itsOperator, chainName_, trustedChainNames, trustedAddresses);

        // InterchainTokenFactory
        itfOwner = admin;

        // iTEL config
        originTEL = devnetTEL;
        originChainName_ = DEVNET_SEPOLIA_CHAIN_NAME;
        symbol_ = "iTEL";
        name_ = "Interchain Telcoin";
        recoverableWindow_ = 60; // 1 minute for devnet
        governanceAddress_ = admin;
        maxToClean = uint16(300);
        baseERC20_ = wtel; 

        // iTELTokenManager config
        tmOperator = AddressBytes.toBytes(governanceAddress_);
        tokenAddress = itel;
        params = abi.encode(tmOperator, tokenAddress);

        // stored for asserts
        abiEncodedWeightedSigners = abi.encode(weightedSigners);
    }
}
